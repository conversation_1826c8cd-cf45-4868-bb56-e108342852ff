<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>Library - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="Introduction" href="introduction.html" />
  <link rel="prev" title="Examples" href="../getting-started/examples.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
    <li>Library</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="../getting-started/examples.html"
       title="previous chapter">← Examples</a>
  </li>
  <li class="next">
    <a href="introduction.html"
       title="next chapter">Introduction →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="library">
<h1>Library<a class="headerlink" href="#library" title="Permalink to this headline">¶</a></h1>
<p>This chapter describes basic all of the library’s core.
Solid understanding of foundations will allow you to utilize full potential of the library.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="introduction.html">Introduction</a><ul>
<li class="toctree-l2"><a class="reference internal" href="introduction.html#arduino-boilerplate">Arduino Boilerplate</a></li>
<li class="toctree-l2"><a class="reference internal" href="introduction.html#esp32-esp8266-boilerplate">ESP32/ESP8266 Boilerplate</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="device-configuration.html">Device configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="device-configuration.html#unique-id">Unique ID</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-configuration.html#device-properties">Device properties</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="availability-reporting.html">Availability reporting</a><ul>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html#shared-availability">Shared availability</a></li>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html#mqtt-lwt">MQTT LWT</a></li>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html#device-type-s-availability">Device type’s availability</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="connection-params.html">Connection parameters</a></li>
<li class="toctree-l1"><a class="reference internal" href="discovery.html">Discovery</a><ul>
<li class="toctree-l2"><a class="reference internal" href="discovery.html#topics-prefix">Topics prefix</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="device-types.html">Device types (entities)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="device-types.html#identifiers">Identifiers</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types.html#limitations">Limitations</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types.html#supported-device-types">Supported device types</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="mqtt-security.html">MQTT security</a></li>
<li class="toctree-l1"><a class="reference internal" href="mqtt-advanced.html">MQTT advanced features</a><ul>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html#callbacks">Callbacks</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html#subscriptions">Subscriptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html#publishing-a-message">Publishing a message</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="compiler-macros.html">Compiler macros</a><ul>
<li class="toctree-l2"><a class="reference internal" href="compiler-macros.html#debug-mode">Debug mode</a></li>
<li class="toctree-l2"><a class="reference internal" href="compiler-macros.html#code-optimization">Code optimization</a></li>
</ul>
</li>
</ul>
</div>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="../getting-started/examples.html"
       title="previous chapter">← Examples</a>
  </li>
  <li class="next">
    <a href="introduction.html"
       title="next chapter">Introduction →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>