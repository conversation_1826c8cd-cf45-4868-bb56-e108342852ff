<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>ArduinoHA documentation</title>
    
          <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="_static/theme-vendors.js"></script> -->
      <script src="_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="Getting started" href="documents/getting-started/index.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="#" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="documents/getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="#">Docs</a> &raquo;</li>
    
    <li>ArduinoHA documentation</li>
  </ul>
  

  <ul class="page-nav">
  <li class="next">
    <a href="documents/getting-started/index.html"
       title="next chapter">Getting started →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="arduinoha-documentation">
<h1>ArduinoHA documentation<a class="headerlink" href="#arduinoha-documentation" title="Permalink to this headline">¶</a></h1>
<p>ArduinoHA allows to integrate an Arduino/ESP based device with Home Assistant using MQTT.
The library is designed to use as low resources (RAM/flash) as possible.
Initially, it was optimized to work on Arduino Uno with Ethernet Shield,
but I successfully use it on ESP8266/ESP8255 boards in my projects.</p>
<section id="features">
<h2>Features<a class="headerlink" href="#features" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li><p>Two-way communication (state reporting and command execution)</p></li>
<li><p>MQTT discovery (device is added to the Home Assistant panel automatically)</p></li>
<li><p>MQTT Last Will and Testament</p></li>
<li><p>Support for custom MQTT messages (publishing and subscribing)</p></li>
<li><p>Auto reconnect with MQTT broker</p></li>
<li><p>Reporting availability (online/offline states) of a device</p></li>
<li><p>Doxygen documentation</p></li>
<li><p>Covered by unit tests (AUnit + EpoxyDuino + AUniter)</p></li>
</ul>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="documents/getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/installation.html">Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/getting-started/installation.html#arduino-ide">Arduino IDE</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/getting-started/installation.html#makeesparduino">makeEspArduino</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/library/introduction.html">Introduction</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/introduction.html#arduino-boilerplate">Arduino Boilerplate</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/introduction.html#esp32-esp8266-boilerplate">ESP32/ESP8266 Boilerplate</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-configuration.html">Device configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/device-configuration.html#unique-id">Unique ID</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/device-configuration.html#device-properties">Device properties</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/availability-reporting.html">Availability reporting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/availability-reporting.html#shared-availability">Shared availability</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/availability-reporting.html#mqtt-lwt">MQTT LWT</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/availability-reporting.html#device-type-s-availability">Device type’s availability</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/discovery.html">Discovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/discovery.html#topics-prefix">Topics prefix</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-types.html">Device types (entities)</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/device-types.html#identifiers">Identifiers</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/device-types.html#limitations">Limitations</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/device-types.html#supported-device-types">Supported device types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-advanced.html">MQTT advanced features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/mqtt-advanced.html#callbacks">Callbacks</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/mqtt-advanced.html#subscriptions">Subscriptions</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/mqtt-advanced.html#publishing-a-message">Publishing a message</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/compiler-macros.html">Compiler macros</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/library/compiler-macros.html#debug-mode">Debug mode</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/library/compiler-macros.html#code-optimization">Code optimization</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/api/core/index.html">Core API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/api/core/ha-device.html">HADevice class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/core/ha-mqtt.html">HAMqtt class</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/device-types/index.html">Device types API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-base-device-type.html">HABaseDeviceType class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-binary-sensor.html">HABinarySensor class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-button.html">HAButton class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-camera.html">HACamera class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-cover.html">HACover class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-device-tracker.html">HADeviceTracker class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-device-trigger.html">HADeviceTrigger class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-fan.html">HAFan class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-hvac.html">HAHVAC class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-light.html">HALight class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-lock.html">HALock class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-number.html">HANumber class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-scene.html">HAScene class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-select.html">HASelect class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-sensor.html">HASensor class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-sensor-number.html">HASensorNumber class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-switch.html">HASwitch class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/device-types/ha-tag-scanner.html">HATagScanner class</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/utils/index.html">Utils API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="documents/api/utils/ha-numeric.html">HANumeric class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/utils/ha-serializer.html">HASerializer class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/utils/ha-serializer-array.html">HASerializerArray class</a></li>
<li class="toctree-l3"><a class="reference internal" href="documents/api/utils/ha-utils.html">HAUtils class</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>
</div>
</section>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="next">
    <a href="documents/getting-started/index.html"
       title="next chapter">Getting started →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>