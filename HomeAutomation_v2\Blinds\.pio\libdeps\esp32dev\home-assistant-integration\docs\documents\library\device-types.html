<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>Device types (entities) - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="MQTT security" href="mqtt-security.html" />
  <link rel="prev" title="Discovery" href="discovery.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Library</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="discovery.html">Discovery</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="index.html">Library</a> &raquo;</li>
    
    <li>Device types (entities)</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="discovery.html"
       title="previous chapter">← Discovery</a>
  </li>
  <li class="next">
    <a href="mqtt-security.html"
       title="next chapter">MQTT security →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="device-types-entities">
<h1>Device types (entities)<a class="headerlink" href="#device-types-entities" title="Permalink to this headline">¶</a></h1>
<p>Device type represents a single entity within the Home Assistant panel, which could be a sensor, lock, camera, or any other item listed in the table below.</p>
<p>Your physical device, such as an ESP-01 board, can have multiple device types assigned to it.
These types will then appear as child entities in the Home Assistant panel.</p>
<section id="identifiers">
<h2>Identifiers<a class="headerlink" href="#identifiers" title="Permalink to this headline">¶</a></h2>
<p>Home Assistant utilizes three distinct identifiers, which might initially appear confusing.
Grasping the purpose of each is crucial for a clear understanding of the library’s API.</p>
<section id="entity-id">
<h3>Entity ID<a class="headerlink" href="#entity-id" title="Permalink to this headline">¶</a></h3>
<p>Home Assistant automatically generates an entity ID for each device type registered by your device.
This ID is primarily utilized by dashboards and automations within Home Assistant.</p>
<p>When the entity is discovered by Home Assistant for the first time, the name field is automatically employed to generate the entity ID.
Once registered, you can modify it to any desired value using the Home Assistant User Interface.</p>
<p>Home Assistant internally relies on the unique ID, so changing the entity ID or name in the Home Assistant UI does not break the integration between your device and Home Assistant.</p>
</section>
<section id="object-id">
<h3>Object ID<a class="headerlink" href="#object-id" title="Permalink to this headline">¶</a></h3>
<p>The object ID is an optional identifier that you can assign to the device type.
Its sole purpose is for generating the entity ID described above.</p>
<p>By default, Home Assistant generates the entity ID based on the entity’s name.
However, when the object ID is provided, Home Assistant uses it to generate the entity ID.</p>
<p>Consequently, you can use the entity’s name as a user-friendly label and the object ID as an internal identifier.</p>
</section>
<section id="unique-id">
<h3>Unique ID<a class="headerlink" href="#unique-id" title="Permalink to this headline">¶</a></h3>
<p>The unique ID serves as an internal identifier for the entity within the Home Assistant instance.
Once the entity with a specific unique ID is created, it cannot be altered, as this identifier is not accessible through the user interface.</p>
<p>Home Assistant utilizes this identifier internally to store the parameters of the entity in the database.
Given that the unique ID must be unique across the entire Home Assistant instance.
Multiple devices cannot expose entities with the same unique ID.</p>
<p>By default, the library uses the unique ID provided in the device type’s constructor.
However, when you reuse the same codebase on multiple devices, conflicts may arise.
To address this issue, you can enable the extended unique ID feature in the HADevice instance.
This feature incorporates the device’s unique ID as a prefix for the device type’s ID.</p>
<div class="literal-block-wrapper docutils container" id="id1">
<div class="code-block-caption"><span class="caption-text">Default behavior</span><a class="headerlink" href="#id1" title="Permalink to this code">¶</a></div>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;Ethernet.h&gt;</span><span class="cp"></span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">byte</span><span class="w"> </span><span class="n">mac</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mh">0x00</span><span class="p">,</span><span class="w"> </span><span class="mh">0x10</span><span class="p">,</span><span class="w"> </span><span class="mh">0xFA</span><span class="p">,</span><span class="w"> </span><span class="mh">0x6E</span><span class="p">,</span><span class="w"> </span><span class="mh">0x38</span><span class="p">,</span><span class="w"> </span><span class="mh">0x4A</span><span class="p">};</span><span class="w"></span>

<span class="n">EthernetClient</span><span class="w"> </span><span class="n">client</span><span class="p">;</span><span class="w"></span>
<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="n">mac</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">mac</span><span class="p">));</span><span class="w"> </span><span class="c1">// the unique ID of the device will be 0010fa6e384a</span>
<span class="n">HAMqtt</span><span class="w"> </span><span class="nf">mqtt</span><span class="p">(</span><span class="n">client</span><span class="p">,</span><span class="w"> </span><span class="n">device</span><span class="p">);</span><span class="w"></span>

<span class="c1">// &quot;myValve&quot; is unique ID of the sensor. You should define your own ID.</span>
<span class="n">HASensor</span><span class="w"> </span><span class="nf">valve</span><span class="p">(</span><span class="s">&quot;myValve&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>

<span class="w">    </span><span class="n">valve</span><span class="p">.</span><span class="n">setIcon</span><span class="p">(</span><span class="s">&quot;mdi:home&quot;</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">valve</span><span class="p">.</span><span class="n">setName</span><span class="p">(</span><span class="s">&quot;Water valve&quot;</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// the unique ID of the valve in HA will be &quot;myValve&quot;</span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</div>
<div class="literal-block-wrapper docutils container" id="id2">
<div class="code-block-caption"><span class="caption-text">Extended unique IDs</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;Ethernet.h&gt;</span><span class="cp"></span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">byte</span><span class="w"> </span><span class="n">mac</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mh">0x00</span><span class="p">,</span><span class="w"> </span><span class="mh">0x10</span><span class="p">,</span><span class="w"> </span><span class="mh">0xFA</span><span class="p">,</span><span class="w"> </span><span class="mh">0x6E</span><span class="p">,</span><span class="w"> </span><span class="mh">0x38</span><span class="p">,</span><span class="w"> </span><span class="mh">0x4A</span><span class="p">};</span><span class="w"></span>

<span class="n">EthernetClient</span><span class="w"> </span><span class="n">client</span><span class="p">;</span><span class="w"></span>
<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="n">mac</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">mac</span><span class="p">));</span><span class="w"> </span><span class="c1">// the unique ID of the device will be 0010fa6e384a</span>
<span class="n">HAMqtt</span><span class="w"> </span><span class="nf">mqtt</span><span class="p">(</span><span class="n">client</span><span class="p">,</span><span class="w"> </span><span class="n">device</span><span class="p">);</span><span class="w"></span>

<span class="c1">// &quot;myValve&quot; is unique ID of the sensor. You should define your own ID.</span>
<span class="n">HASensor</span><span class="w"> </span><span class="nf">valve</span><span class="p">(</span><span class="s">&quot;myValve&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>

<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">enableExtendedUniqueIds</span><span class="p">();</span><span class="w"> </span><span class="c1">// &lt;------------ enables extended unique IDs</span>
<span class="w">    </span><span class="n">valve</span><span class="p">.</span><span class="n">setIcon</span><span class="p">(</span><span class="s">&quot;mdi:home&quot;</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">valve</span><span class="p">.</span><span class="n">setName</span><span class="p">(</span><span class="s">&quot;Water valve&quot;</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// the unique ID of the valve in HA will be &quot;0010fa6e384a_myValve&quot;</span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</div>
</section>
</section>
<section id="limitations">
<h2>Limitations<a class="headerlink" href="#limitations" title="Permalink to this headline">¶</a></h2>
<p>Registering a new device type involves utilizing a certain amount of flash and RAM memory.
On less powerful units, such as the Arduino Uno, you may rapidly reach the resource limit.
Therefore, it is advisable to keep the device simple to avoid hitting the resource limit, which could lead to random reboots of the device.</p>
<p>By default, devices utilizing ATmega328P or ATmega168 processors are limited to a maximum of 6 device types, while other platforms can support up to 24 device types.
You can increase the limit using the <a class="reference internal" href="../api/core/ha-mqtt.html"><span class="doc">HAMqtt</span></a> class constructor as follows:</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;Ethernet.h&gt;</span><span class="cp"></span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">byte</span><span class="w"> </span><span class="n">mac</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mh">0x00</span><span class="p">,</span><span class="w"> </span><span class="mh">0x10</span><span class="p">,</span><span class="w"> </span><span class="mh">0xFA</span><span class="p">,</span><span class="w"> </span><span class="mh">0x6E</span><span class="p">,</span><span class="w"> </span><span class="mh">0x38</span><span class="p">,</span><span class="w"> </span><span class="mh">0x4A</span><span class="p">};</span><span class="w"></span>
<span class="n">EthernetClient</span><span class="w"> </span><span class="n">client</span><span class="p">;</span><span class="w"></span>
<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="n">mac</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">mac</span><span class="p">));</span><span class="w"></span>
<span class="n">HAMqtt</span><span class="w"> </span><span class="nf">mqtt</span><span class="p">(</span><span class="n">client</span><span class="p">,</span><span class="w"> </span><span class="n">device</span><span class="p">,</span><span class="w"> </span><span class="mi">40</span><span class="p">);</span><span class="w"> </span><span class="c1">// &lt;------------ 40 <USER> <GROUP> new limit of device types</span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">Ethernet</span><span class="p">.</span><span class="n">begin</span><span class="p">(</span><span class="n">mac</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// your setup logic goes here</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">Ethernet</span><span class="p">.</span><span class="n">maintain</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">mqtt</span><span class="p">.</span><span class="n">loop</span><span class="p">();</span><span class="w"></span>

<span class="w">    </span><span class="c1">// your loop logic goes here</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>Please note that this limit cannot be changed at runtime.</p>
</section>
<section id="supported-device-types">
<h2>Supported device types<a class="headerlink" href="#supported-device-types" title="Permalink to this headline">¶</a></h2>
<table class="colwidths-given supported-device-types-table docutils align-default">
<colgroup>
<col style="width: 25%" />
<col style="width: 15%" />
<col style="width: 60%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Device type</p></th>
<th class="head"><p>Supported</p></th>
<th class="head"><p>Documentation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Alarm control panel</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-odd"><td><p>Binary sensor</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-binary-sensor.html"><span class="doc">HABinarySensor</span></a></p></td>
</tr>
<tr class="row-even"><td><p>Button</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-button.html"><span class="doc">HAButton</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Camera</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-camera.html"><span class="doc">HACamera</span></a></p></td>
</tr>
<tr class="row-even"><td><p>Cover</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-cover.html"><span class="doc">HACover</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Device tracker</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-device-tracker.html"><span class="doc">HADeviceTracker</span></a></p></td>
</tr>
<tr class="row-even"><td><p>Device trigger</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-device-trigger.html"><span class="doc">HADeviceTrigger</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Event</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>Fan</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-fan.html"><span class="doc">HAFan</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Humidifier</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>HVAC</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-hvac.html"><span class="doc">HAHVAC</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Lawn mower</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>Light</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-light.html"><span class="doc">HALight</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Lock</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-lock.html"><span class="doc">HALock</span></a></p></td>
</tr>
<tr class="row-even"><td><p>Number</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-number.html"><span class="doc">HANumber</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Scene</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-scene.html"><span class="doc">HAScene</span></a></p></td>
</tr>
<tr class="row-even"><td><p>Select</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-select.html"><span class="doc">HASelect</span></a></p></td>
</tr>
<tr class="row-odd"><td><div class="line-block">
<div class="line">Sensor (text)</div>
</div>
</td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-sensor.html"><span class="doc">HASensor</span></a></p></td>
</tr>
<tr class="row-even"><td><div class="line-block">
<div class="line">Sensor (number)</div>
</div>
</td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-sensor-number.html"><span class="doc">HASensorNumber</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Siren</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>Switch</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-switch.html"><span class="doc">HASwitch</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Update</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>Tag scanner</p></td>
<td><p>✅</p></td>
<td><p><a class="reference internal" href="../api/device-types/ha-tag-scanner.html"><span class="doc">HATagScanner</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Text</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>Vacuum</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-odd"><td><p>Valve</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
<tr class="row-even"><td><p>Water heater</p></td>
<td><p>❌</p></td>
<td><p>–</p></td>
</tr>
</tbody>
</table>
</section>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="discovery.html"
       title="previous chapter">← Discovery</a>
  </li>
  <li class="next">
    <a href="mqtt-security.html"
       title="next chapter">MQTT security →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>