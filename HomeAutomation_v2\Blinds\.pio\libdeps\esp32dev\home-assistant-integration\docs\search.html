<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
      <title>Search - ArduinoHA</title>
    
          <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  
      
      <!-- sphinx script_files -->
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="_static/theme-vendors.js"></script> -->
      <script src="_static/theme.js" defer></script>
  <script type="text/javascript" src="_static/searchtools.js"></script>
  <script src="_static/language_data.js"></script>
    
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="#" />
  <script type="text/javascript" src="searchindex.js" defer></script>
  
  <script type="text/javascript" id="searchindexloader"></script>
   

  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="#" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="documents/getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="index.html">Docs</a> &raquo;</li>
    
    <li>Search</li>
  </ul>
  

  <ul class="page-nav">
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <h1 id="search-documentation">Search</h1>
  <div id="fallback" class="admonition warning">
  <p>
    Please activate JavaScript to enable the search
    functionality.
  </p>
  </div>
  <p>
    From here you can search these documents. Enter your search
    words into the box below and click "search". Note that the search
    function will automatically search for all of the words. Pages
    containing fewer words won't appear in the result list.
  </p>
  <form action="" method="get">
    <input type="text" name="q" value="" />
    <input type="submit" value="search" />
    <span id="search-progress" style="padding-left: 10px"></span>
  </form>
  
  <div id="search-results">
  
  </div>

          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
  <script type="text/javascript">
    $('#fallback').hide();
  </script>

  </body>
</html>