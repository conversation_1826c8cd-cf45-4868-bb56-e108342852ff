<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>HAButton class - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../../_static/theme-vendors.js"></script> -->
      <script src="../../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../../genindex.html" />
  <link rel="search" title="Search" href="../../../search.html" />
  <link rel="next" title="HACamera class" href="ha-camera.html" />
  <link rel="prev" title="HABinarySensor class" href="ha-binary-sensor.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">API reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../core/index.html">Core API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="../index.html">API reference</a> &raquo;</li>
    
      <li><a href="index.html">Device types API</a> &raquo;</li>
    
    <li>HAButton class</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="ha-binary-sensor.html"
       title="previous chapter">← HABinarySensor class</a>
  </li>
  <li class="next">
    <a href="ha-camera.html"
       title="next chapter">HACamera class →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="habutton-class">
<h1>HAButton class<a class="headerlink" href="#habutton-class" title="Permalink to this headline">¶</a></h1>
<dl class="cpp class">
<dt class="sig sig-object cpp" id="_CPPv48HAButton">
<span id="_CPPv38HAButton"></span><span id="_CPPv28HAButton"></span><span id="HAButton"></span><span class="target" id="class_h_a_button"></span><span class="k"><span class="pre">class</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">HAButton</span></span></span><span class="w"> </span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="k"><span class="pre">public</span></span><span class="w"> </span><a class="reference internal" href="ha-base-device-type.html#_CPPv416HABaseDeviceType" title="HABaseDeviceType"><span class="n"><span class="pre">HABaseDeviceType</span></span></a><a class="headerlink" href="#_CPPv48HAButton" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="#class_h_a_button"><span class="std std-ref">HAButton</span></a> represents a button that’s displayed in the Home Assistant panel and triggers some logic on your Arduino/ESP device once clicked.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You can find more information about this entity in the Home Assistant documentation: <a class="reference external" href="https://www.home-assistant.io/integrations/button.mqtt/">https://www.home-assistant.io/integrations/button.mqtt/</a> </p>
</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-functions">Public Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton8HAButtonEPKc">
<span id="_CPPv3N8HAButton8HAButtonEPKc"></span><span id="_CPPv2N8HAButton8HAButtonEPKc"></span><span id="HAButton::HAButton__cCP"></span><span class="target" id="class_h_a_button_1a9ef3a2c0445e45b0a0d4c32dfe4abe66"></span><span class="sig-name descname"><span class="n"><span class="pre">HAButton</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">uniqueId</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N8HAButton8HAButtonEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>uniqueId</strong> – The unique ID of the button. It needs to be unique in a scope of your device. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton14setDeviceClassEPKc">
<span id="_CPPv3N8HAButton14setDeviceClassEPKc"></span><span id="_CPPv2N8HAButton14setDeviceClassEPKc"></span><span id="HAButton::setDeviceClass__cCP"></span><span class="target" id="class_h_a_button_1a0d898b35e8b81a3071c7b18c2f8e8447"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setDeviceClass</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">deviceClass</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N8HAButton14setDeviceClassEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets class of the device. You can find list of available values here: <a class="reference external" href="https://www.home-assistant.io/integrations/button/#device-class">https://www.home-assistant.io/integrations/button/#device-class</a></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>deviceClass</strong> – The class name. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton7setIconEPKc">
<span id="_CPPv3N8HAButton7setIconEPKc"></span><span id="_CPPv2N8HAButton7setIconEPKc"></span><span id="HAButton::setIcon__cCP"></span><span class="target" id="class_h_a_button_1a23593271777ec5a5f905853470f13294"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setIcon</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">icon</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N8HAButton7setIconEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets icon of the button. Any icon from MaterialDesignIcons.com (for example: <code class="docutils literal notranslate"><span class="pre">mdi:home</span></code>).</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>icon</strong> – The icon name. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton9setRetainEKb">
<span id="_CPPv3N8HAButton9setRetainEKb"></span><span id="_CPPv2N8HAButton9setRetainEKb"></span><span id="HAButton::setRetain__bC"></span><span class="target" id="class_h_a_button_1a0d20026ca8212f41fc36056995624f01"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setRetain</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">retain</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N8HAButton9setRetainEKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets retain flag for the button’s command. If set to <code class="docutils literal notranslate"><span class="pre">true</span></code> the command produced by Home Assistant will be retained.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>retain</strong> – </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton9onCommandEPFvP8HAButtonE">
<span id="_CPPv3N8HAButton9onCommandEPFvP8HAButtonE"></span><span id="_CPPv2N8HAButton9onCommandEPFvP8HAButtonE"></span><span class="target" id="class_h_a_button_1a5d81f6179db100b63b8b97433416b3fb"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onCommand</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">callback</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#_CPPv48HAButton" title="HAButton"><span class="n"><span class="pre">HAButton</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">sender</span></span><span class="p"><span class="pre">)</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N8HAButton9onCommandEPFvP8HAButtonE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Registers callback that will be called each time the press command from HA is received. Please note that it’s not possible to register multiple callbacks for the same button.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>callback</strong> – </p>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-protected-functions">Protected Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton15buildSerializerEv">
<span id="_CPPv3N8HAButton15buildSerializerEv"></span><span id="_CPPv2N8HAButton15buildSerializerEv"></span><span id="HAButton::buildSerializer"></span><span class="target" id="class_h_a_button_1a009ae671939f154fbd624fafd30d6556"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buildSerializer</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N8HAButton15buildSerializerEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton15onMqttConnectedEv">
<span id="_CPPv3N8HAButton15onMqttConnectedEv"></span><span id="_CPPv2N8HAButton15onMqttConnectedEv"></span><span id="HAButton::onMqttConnected"></span><span class="target" id="class_h_a_button_1afc35e767cbd52907fe5282c0598119f5"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttConnected</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N8HAButton15onMqttConnectedEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton13onMqttMessageEPKcPK7uint8_tK8uint16_t">
<span id="_CPPv3N8HAButton13onMqttMessageEPKcPK7uint8_tK8uint16_t"></span><span id="_CPPv2N8HAButton13onMqttMessageEPKcPK7uint8_tK8uint16_t"></span><span id="HAButton::onMqttMessage__cCP.uint8_tCP.uint16_tC"></span><span class="target" id="class_h_a_button_1a13bc55638f22b2193665e0f32fb4cc42"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttMessage</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">topic</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint8_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">payload</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">length</span></span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N8HAButton13onMqttMessageEPKcPK7uint8_tK8uint16_t" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-members">Private Members</p>
<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton6_classE">
<span id="_CPPv3N8HAButton6_classE"></span><span id="_CPPv2N8HAButton6_classE"></span><span id="HAButton::_class__cCP"></span><span class="target" id="class_h_a_button_1a872f6b959ec4bc9e7044256fbb44366b"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_class</span></span></span><a class="headerlink" href="#_CPPv4N8HAButton6_classE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The device class. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton5_iconE">
<span id="_CPPv3N8HAButton5_iconE"></span><span id="_CPPv2N8HAButton5_iconE"></span><span id="HAButton::_icon__cCP"></span><span class="target" id="class_h_a_button_1a17873a37496c6ca8ad22f9c964cf2f21"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_icon</span></span></span><a class="headerlink" href="#_CPPv4N8HAButton5_iconE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The icon of the button. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton7_retainE">
<span id="_CPPv3N8HAButton7_retainE"></span><span id="_CPPv2N8HAButton7_retainE"></span><span id="HAButton::_retain__b"></span><span class="target" id="class_h_a_button_1a68d6edf1e376a380501f2de0c73c6954"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_retain</span></span></span><a class="headerlink" href="#_CPPv4N8HAButton7_retainE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The retain flag for the HA commands. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N8HAButton16_commandCallbackE">
<span id="_CPPv3N8HAButton16_commandCallbackE"></span><span id="_CPPv2N8HAButton16_commandCallbackE"></span><span class="target" id="class_h_a_button_1a5d15c00c7aa6562f3f272e647530ef48"></span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_commandCallback</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#_CPPv48HAButton" title="HAButton"><span class="n"><span class="pre">HAButton</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">sender</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#_CPPv4N8HAButton16_commandCallbackE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The command callback that will be called once clicking the button in HA panel. </p>
</dd></dl>

</div>
</dd></dl>

</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="ha-binary-sensor.html"
       title="previous chapter">← HABinarySensor class</a>
  </li>
  <li class="next">
    <a href="ha-camera.html"
       title="next chapter">HACamera class →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>