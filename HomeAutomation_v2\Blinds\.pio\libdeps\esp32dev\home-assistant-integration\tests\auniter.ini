[auniter]
  monitor = picocom -b $baud --omap crlf --imap lfcrlf --echo $port

[boards]
  uno = arduino:avr:uno
  mega = arduino:avr:mega:cpu=atmega2560
  nodemcuv2 = esp8266:esp8266:nodemcuv2:xtal=80,eesz=4M,ip=lm2f,dbg=Disabled,lvl=None____,wipe=none,baud=921600
  esp32 = esp32:esp32:esp32da:PartitionScheme=default,EraseFlash=none,FlashMode=qio,FlashFreq=80,FlashSize=4M,LoopCore=1,EventsCore=1,UploadSpeed=921600,DebugLevel=none
  due = arduino:sam:arduino_due_x_dbg

[env:uno]
  board = uno
  preprocessor = -D AUNITER_UNO -D ARDUINOHA_TEST

[env:mega]
  board = mega
  preprocessor = -D AUNITER_MEGA -D ARDUINOHA_TEST

[env:esp8266]
  board = nodemcuv2
  preprocessor = -D AUNITER_ESP8266 -D ARDUINOHA_TEST

[env:esp32]
  board = esp32
  preprocessor = -D AUNITER_ESP32 -D ARDUINOHA_TEST

[env:due]
  board = due
  preprocessor = -D AUNITER_DUE -D ARDUINOHA_TEST
