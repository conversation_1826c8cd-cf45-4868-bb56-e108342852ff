
<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
      <title>Index - ArduinoHA</title>
    
          <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="_static/theme-vendors.js"></script> -->
      <script src="_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="#" />
  <link rel="search" title="Search" href="search.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="documents/getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="documents/api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documents/api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="documents/api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="index.html">Docs</a> &raquo;</li>
    
    <li>Index</li>
  </ul>
  

  <ul class="page-nav">
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#H"><strong>H</strong></a>
 
</div>
<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv416HABaseDeviceType">HABaseDeviceType (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType13_availabilityE">HABaseDeviceType::_availability (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType14_componentNameE">HABaseDeviceType::_componentName (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType5_nameE">HABaseDeviceType::_name (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType9_objectIdE">HABaseDeviceType::_objectId (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType11_serializerE">HABaseDeviceType::_serializer (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType9_uniqueIdE">HABaseDeviceType::_uniqueId (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType12AvailabilityE">HABaseDeviceType::Availability (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType12Availability19AvailabilityDefaultE">HABaseDeviceType::Availability::AvailabilityDefault (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType12Availability19AvailabilityOfflineE">HABaseDeviceType::Availability::AvailabilityOffline (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType12Availability18AvailabilityOnlineE">HABaseDeviceType::Availability::AvailabilityOnline (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15buildSerializerEv">HABaseDeviceType::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4NK16HABaseDeviceType13componentNameEv">HABaseDeviceType::componentName (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType17destroySerializerEv">HABaseDeviceType::destroySerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4NK16HABaseDeviceType7getNameEv">HABaseDeviceType::getName (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4NK16HABaseDeviceType11getObjectIdEv">HABaseDeviceType::getObjectId (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType16HABaseDeviceTypeEPK19__FlashStringHelperPKc">HABaseDeviceType::HABaseDeviceType (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4NK16HABaseDeviceType24isAvailabilityConfiguredEv">HABaseDeviceType::isAvailabilityConfigured (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4NK16HABaseDeviceType8isOnlineEv">HABaseDeviceType::isOnline (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType4mqttEv">HABaseDeviceType::mqtt (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15NumberPrecisionE">HABaseDeviceType::NumberPrecision (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15NumberPrecision11PrecisionP0E">HABaseDeviceType::NumberPrecision::PrecisionP0 (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15NumberPrecision11PrecisionP1E">HABaseDeviceType::NumberPrecision::PrecisionP1 (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15NumberPrecision11PrecisionP2E">HABaseDeviceType::NumberPrecision::PrecisionP2 (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15NumberPrecision11PrecisionP3E">HABaseDeviceType::NumberPrecision::PrecisionP3 (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15onMqttConnectedEv">HABaseDeviceType::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType13onMqttMessageEPKcPK7uint8_tK8uint16_t">HABaseDeviceType::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType19publishAvailabilityEv">HABaseDeviceType::publishAvailability (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType13publishConfigEv">HABaseDeviceType::publishConfig (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType18publishOnDataTopicEPK19__FlashStringHelperPK19__FlashStringHelperb">HABaseDeviceType::publishOnDataTopic (C++ function)</a>, <a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType18publishOnDataTopicEPK19__FlashStringHelperPK7uint8_tK8uint16_tbb">[1]</a>, <a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType18publishOnDataTopicEPK19__FlashStringHelperPKcb">[2]</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType15setAvailabilityEb">HABaseDeviceType::setAvailability (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType7setNameEPKc">HABaseDeviceType::setName (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType11setObjectIdEPKc">HABaseDeviceType::setObjectId (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4N16HABaseDeviceType14subscribeTopicEPKcPK19__FlashStringHelper">HABaseDeviceType::subscribeTopic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-base-device-type.html#_CPPv4NK16HABaseDeviceType8uniqueIdEv">HABaseDeviceType::uniqueId (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv414HABinarySensor">HABinarySensor (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor6_classE">HABinarySensor::_class (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor13_currentStateE">HABinarySensor::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor12_expireAfterE">HABinarySensor::_expireAfter (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor5_iconE">HABinarySensor::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor15buildSerializerEv">HABinarySensor::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4NK14HABinarySensor15getCurrentStateEv">HABinarySensor::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor14HABinarySensorEPKc">HABinarySensor::HABinarySensor (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor15onMqttConnectedEv">HABinarySensor::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor12publishStateEb">HABinarySensor::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor15setCurrentStateEKb">HABinarySensor::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor14setDeviceClassEPKc">HABinarySensor::setDeviceClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor14setExpireAfterE8uint16_t">HABinarySensor::setExpireAfter (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor7setIconEPKc">HABinarySensor::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-binary-sensor.html#_CPPv4N14HABinarySensor8setStateEKbKb">HABinarySensor::setState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv48HAButton">HAButton (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton6_classE">HAButton::_class (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton16_commandCallbackE">HAButton::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton5_iconE">HAButton::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton7_retainE">HAButton::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton15buildSerializerEv">HAButton::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton8HAButtonEPKc">HAButton::HAButton (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton9onCommandEPFvP8HAButtonE">HAButton::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton15onMqttConnectedEv">HAButton::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton13onMqttMessageEPKcPK7uint8_tK8uint16_t">HAButton::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton14setDeviceClassEPKc">HAButton::setDeviceClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton7setIconEPKc">HAButton::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-button.html#_CPPv4N8HAButton9setRetainEKb">HAButton::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv48HACamera">HACamera (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera9_encodingE">HACamera::_encoding (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera5_iconE">HACamera::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera15buildSerializerEv">HACamera::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4NK8HACamera19getEncodingPropertyEv">HACamera::getEncodingProperty (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera8HACameraEPKc">HACamera::HACamera (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera13ImageEncodingE">HACamera::ImageEncoding (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera13ImageEncoding14EncodingBase64E">HACamera::ImageEncoding::EncodingBase64 (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera13ImageEncoding14EncodingBinaryE">HACamera::ImageEncoding::EncodingBinary (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera15onMqttConnectedEv">HACamera::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera12publishImageEPK7uint8_tK8uint16_t">HACamera::publishImage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera11setEncodingEK13ImageEncoding">HACamera::setEncoding (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-camera.html#_CPPv4N8HACamera7setIconEPKc">HACamera::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv47HACover">HACover (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover6_classE">HACover::_class (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover16_commandCallbackE">HACover::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover16_currentPositionE">HACover::_currentPosition (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover13_currentStateE">HACover::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover9_featuresE">HACover::_features (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover5_iconE">HACover::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover11_optimisticE">HACover::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover7_retainE">HACover::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover15buildSerializerEv">HACover::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover12CoverCommandE">HACover::CoverCommand (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover12CoverCommand12CommandCloseE">HACover::CoverCommand::CommandClose (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover12CoverCommand11CommandOpenE">HACover::CoverCommand::CommandOpen (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover12CoverCommand11CommandStopE">HACover::CoverCommand::CommandStop (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverStateE">HACover::CoverState (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverState11StateClosedE">HACover::CoverState::StateClosed (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverState12StateClosingE">HACover::CoverState::StateClosing (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverState9StateOpenE">HACover::CoverState::StateOpen (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverState12StateOpeningE">HACover::CoverState::StateOpening (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverState12StateStoppedE">HACover::CoverState::StateStopped (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover10CoverState12StateUnknownE">HACover::CoverState::StateUnknown (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover15DefaultPositionE">HACover::DefaultPosition (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover8FeaturesE">HACover::Features (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover8Features15DefaultFeaturesE">HACover::Features::DefaultFeatures (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover8Features15PositionFeatureE">HACover::Features::PositionFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4NK7HACover18getCurrentPositionEv">HACover::getCurrentPosition (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4NK7HACover15getCurrentStateEv">HACover::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover7HACoverEPKcK8Features">HACover::HACover (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover13handleCommandEPK7uint8_tK8uint16_t">HACover::handleCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover9onCommandEPFv12CoverCommandP7HACoverE">HACover::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover15onMqttConnectedEv">HACover::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover13onMqttMessageEPKcPK7uint8_tK8uint16_t">HACover::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover15publishPositionEK7int16_t">HACover::publishPosition (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover12publishStateEK10CoverState">HACover::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover18setCurrentPositionEK7int16_t">HACover::setCurrentPosition (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover15setCurrentStateEK10CoverState">HACover::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover14setDeviceClassEPKc">HACover::setDeviceClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover7setIconEPKc">HACover::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover13setOptimisticEKb">HACover::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover11setPositionEK7int16_tKb">HACover::setPosition (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover9setRetainEKb">HACover::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-cover.html#_CPPv4N7HACover8setStateEK10CoverStateKb">HACover::setState (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv48HADevice">HADevice (C++ class)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice18_availabilityTopicE">HADevice::_availabilityTopic (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice10_availableE">HADevice::_available (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice18_extendedUniqueIdsE">HADevice::_extendedUniqueIds (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice13_ownsUniqueIdE">HADevice::_ownsUniqueId (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice11_serializerE">HADevice::_serializer (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice19_sharedAvailabilityE">HADevice::_sharedAvailability (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice9_uniqueIdE">HADevice::_uniqueId (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice23enableExtendedUniqueIdsEv">HADevice::enableExtendedUniqueIds (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice14enableLastWillEv">HADevice::enableLastWill (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice24enableSharedAvailabilityEv">HADevice::enableSharedAvailability (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice20getAvailabilityTopicEv">HADevice::getAvailabilityTopic (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice13getSerializerEv">HADevice::getSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice11getUniqueIdEv">HADevice::getUniqueId (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice8HADeviceEPK4byteK8uint16_t">HADevice::HADevice (C++ function)</a>, <a href="documents/api/core/ha-device.html#_CPPv4N8HADevice8HADeviceEPKc">[1]</a>, <a href="documents/api/core/ha-device.html#_CPPv4N8HADevice8HADeviceEv">[2]</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice11isAvailableEv">HADevice::isAvailable (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice26isExtendedUniqueIdsEnabledEv">HADevice::isExtendedUniqueIdsEnabled (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice27isSharedAvailabilityEnabledEv">HADevice::isSharedAvailabilityEnabled (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4NK8HADevice19publishAvailabilityEv">HADevice::publishAvailability (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice15setAvailabilityEb">HADevice::setAvailability (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice19setConfigurationUrlEPKc">HADevice::setConfigurationUrl (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice15setManufacturerEPKc">HADevice::setManufacturer (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice8setModelEPKc">HADevice::setModel (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice7setNameEPKc">HADevice::setName (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice18setSoftwareVersionEPKc">HADevice::setSoftwareVersion (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADevice11setUniqueIdEPK4byteK8uint16_t">HADevice::setUniqueId (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-device.html#_CPPv4N8HADeviceD0Ev">HADevice::~HADevice (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv415HADeviceTracker">HADeviceTracker (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker13_currentStateE">HADeviceTracker::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker5_iconE">HADeviceTracker::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker11_sourceTypeE">HADeviceTracker::_sourceType (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker15buildSerializerEv">HADeviceTracker::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4NK15HADeviceTracker21getSourceTypePropertyEv">HADeviceTracker::getSourceTypeProperty (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4NK15HADeviceTracker8getStateEv">HADeviceTracker::getState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker15HADeviceTrackerEPKc">HADeviceTracker::HADeviceTracker (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker15onMqttConnectedEv">HADeviceTracker::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker12publishStateE12TrackerState">HADeviceTracker::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker15setCurrentStateEK12TrackerState">HADeviceTracker::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker7setIconEPKc">HADeviceTracker::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker13setSourceTypeEK10SourceType">HADeviceTracker::setSourceType (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker8setStateEK12TrackerStateKb">HADeviceTracker::setState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker10SourceTypeE">HADeviceTracker::SourceType (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker10SourceType19SourceTypeBluetoothE">HADeviceTracker::SourceType::SourceTypeBluetooth (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker10SourceType21SourceTypeBluetoothLEE">HADeviceTracker::SourceType::SourceTypeBluetoothLE (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker10SourceType13SourceTypeGPSE">HADeviceTracker::SourceType::SourceTypeGPS (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker10SourceType16SourceTypeRouterE">HADeviceTracker::SourceType::SourceTypeRouter (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker10SourceType17SourceTypeUnknownE">HADeviceTracker::SourceType::SourceTypeUnknown (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker12TrackerStateE">HADeviceTracker::TrackerState (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker12TrackerState9StateHomeE">HADeviceTracker::TrackerState::StateHome (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker12TrackerState17StateNotAvailableE">HADeviceTracker::TrackerState::StateNotAvailable (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker12TrackerState12StateNotHomeE">HADeviceTracker::TrackerState::StateNotHome (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-tracker.html#_CPPv4N15HADeviceTracker12TrackerState12StateUnknownE">HADeviceTracker::TrackerState::StateUnknown (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv415HADeviceTrigger">HADeviceTrigger (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger17_isProgmemSubtypeE">HADeviceTrigger::_isProgmemSubtype (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14_isProgmemTypeE">HADeviceTrigger::_isProgmemType (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger8_subtypeE">HADeviceTrigger::_subtype (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger5_typeE">HADeviceTrigger::_type (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger15buildSerializerEv">HADeviceTrigger::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger13buildUniqueIdEv">HADeviceTrigger::buildUniqueId (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger15calculateIdSizeEv">HADeviceTrigger::calculateIdSize (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger23determineProgmemSubtypeE14TriggerSubtype">HADeviceTrigger::determineProgmemSubtype (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger20determineProgmemTypeE11TriggerType">HADeviceTrigger::determineProgmemType (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger10getSubtypeEv">HADeviceTrigger::getSubtype (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger7getTypeEv">HADeviceTrigger::getType (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger15HADeviceTriggerE11TriggerType14TriggerSubtype">HADeviceTrigger::HADeviceTrigger (C++ function)</a>, <a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger15HADeviceTriggerE11TriggerTypePKc">[1]</a>, <a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger15HADeviceTriggerEPKc14TriggerSubtype">[2]</a>, <a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger15HADeviceTriggerEPKcPKc">[3]</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger16isProgmemSubtypeEv">HADeviceTrigger::isProgmemSubtype (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4NK15HADeviceTrigger13isProgmemTypeEv">HADeviceTrigger::isProgmemType (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger15onMqttConnectedEv">HADeviceTrigger::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger7triggerEv">HADeviceTrigger::trigger (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtypeE">HADeviceTrigger::TriggerSubtype (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button1SubtypeE">HADeviceTrigger::TriggerSubtype::Button1Subtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button2SubtypeE">HADeviceTrigger::TriggerSubtype::Button2Subtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button3SubtypeE">HADeviceTrigger::TriggerSubtype::Button3Subtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button4SubtypeE">HADeviceTrigger::TriggerSubtype::Button4Subtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button5SubtypeE">HADeviceTrigger::TriggerSubtype::Button5Subtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button6SubtypeE">HADeviceTrigger::TriggerSubtype::Button6Subtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype14TurnOffSubtypeE">HADeviceTrigger::TriggerSubtype::TurnOffSubtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger14TriggerSubtype13TurnOnSubtypeE">HADeviceTrigger::TriggerSubtype::TurnOnSubtype (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerTypeE">HADeviceTrigger::TriggerType (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType21ButtonDoublePressTypeE">HADeviceTrigger::TriggerType::ButtonDoublePressType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType19ButtonLongPressTypeE">HADeviceTrigger::TriggerType::ButtonLongPressType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType21ButtonLongReleaseTypeE">HADeviceTrigger::TriggerType::ButtonLongReleaseType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType24ButtonQuadruplePressTypeE">HADeviceTrigger::TriggerType::ButtonQuadruplePressType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType24ButtonQuintuplePressTypeE">HADeviceTrigger::TriggerType::ButtonQuintuplePressType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType20ButtonShortPressTypeE">HADeviceTrigger::TriggerType::ButtonShortPressType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType22ButtonShortReleaseTypeE">HADeviceTrigger::TriggerType::ButtonShortReleaseType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTrigger11TriggerType21ButtonTriplePressTypeE">HADeviceTrigger::TriggerType::ButtonTriplePressType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-device-trigger.html#_CPPv4N15HADeviceTriggerD0Ev">HADeviceTrigger::~HADeviceTrigger (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv45HAFan">HAFan (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan13_currentSpeedE">HAFan::_currentSpeed (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan13_currentStateE">HAFan::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan9_featuresE">HAFan::_features (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan5_iconE">HAFan::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan11_optimisticE">HAFan::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan7_retainE">HAFan::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan14_speedCallbackE">HAFan::_speedCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan14_speedRangeMaxE">HAFan::_speedRangeMax (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan14_speedRangeMinE">HAFan::_speedRangeMin (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan14_stateCallbackE">HAFan::_stateCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan15buildSerializerEv">HAFan::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan8FeaturesE">HAFan::Features (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan8Features15DefaultFeaturesE">HAFan::Features::DefaultFeatures (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan8Features13SpeedsFeatureE">HAFan::Features::SpeedsFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4NK5HAFan15getCurrentSpeedEv">HAFan::getCurrentSpeed (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4NK5HAFan15getCurrentStateEv">HAFan::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan5HAFanEPKcK7uint8_t">HAFan::HAFan (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan18handleSpeedCommandEPK7uint8_tK8uint16_t">HAFan::handleSpeedCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan18handleStateCommandEPK7uint8_tK8uint16_t">HAFan::handleStateCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan15onMqttConnectedEv">HAFan::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan13onMqttMessageEPKcPK7uint8_tK8uint16_t">HAFan::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan14onSpeedCommandEPFv8uint16_tP5HAFanE">HAFan::onSpeedCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan14onStateCommandEPFvbP5HAFanE">HAFan::onStateCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan12publishSpeedEK8uint16_t">HAFan::publishSpeed (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan12publishStateEKb">HAFan::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan15setCurrentSpeedEK8uint16_t">HAFan::setCurrentSpeed (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan15setCurrentStateEKb">HAFan::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan7setIconEPKc">HAFan::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan13setOptimisticEKb">HAFan::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan9setRetainEKb">HAFan::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan8setSpeedEK8uint16_tKb">HAFan::setSpeed (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan16setSpeedRangeMaxEK8uint16_t">HAFan::setSpeedRangeMax (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan16setSpeedRangeMinEK8uint16_t">HAFan::setSpeedRangeMin (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan8setStateEKbKb">HAFan::setState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan7turnOffEv">HAFan::turnOff (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-fan.html#_CPPv4N5HAFan6turnOnEv">HAFan::turnOn (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv46HAHVAC">HAHVAC (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7_actionE">HAHVAC::_action (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC12_auxCallbackE">HAHVAC::_auxCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9_auxStateE">HAHVAC::_auxState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC19_currentTemperatureE">HAHVAC::_currentTemperature (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8_fanModeE">HAHVAC::_fanMode (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC16_fanModeCallbackE">HAHVAC::_fanModeCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9_fanModesE">HAHVAC::_fanModes (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC19_fanModesSerializerE">HAHVAC::_fanModesSerializer (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9_featuresE">HAHVAC::_features (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC5_iconE">HAHVAC::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8_maxTempE">HAHVAC::_maxTemp (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8_minTempE">HAHVAC::_minTemp (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC5_modeE">HAHVAC::_mode (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC13_modeCallbackE">HAHVAC::_modeCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6_modesE">HAHVAC::_modes (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC16_modesSerializerE">HAHVAC::_modesSerializer (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC14_powerCallbackE">HAHVAC::_powerCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC10_precisionE">HAHVAC::_precision (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7_retainE">HAHVAC::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC10_swingModeE">HAHVAC::_swingMode (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC18_swingModeCallbackE">HAHVAC::_swingModeCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC11_swingModesE">HAHVAC::_swingModes (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21_swingModesSerializerE">HAHVAC::_swingModesSerializer (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC18_targetTemperatureE">HAHVAC::_targetTemperature (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC26_targetTemperatureCallbackE">HAHVAC::_targetTemperatureCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC16_temperatureUnitE">HAHVAC::_temperatureUnit (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9_tempStepE">HAHVAC::_tempStep (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6ActionE">HAHVAC::Action (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action13CoolingActionE">HAHVAC::Action::CoolingAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action12DryingActionE">HAHVAC::Action::DryingAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action9FanActionE">HAHVAC::Action::FanAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action13HeatingActionE">HAHVAC::Action::HeatingAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action10IdleActionE">HAHVAC::Action::IdleAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action9OffActionE">HAHVAC::Action::OffAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6Action13UnknownActionE">HAHVAC::Action::UnknownAction (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15buildSerializerEv">HAHVAC::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15DefaultFanModesE">HAHVAC::DefaultFanModes (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC12DefaultModesE">HAHVAC::DefaultModes (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC17DefaultSwingModesE">HAHVAC::DefaultSwingModes (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7FanModeE">HAHVAC::FanMode (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7FanMode11AutoFanModeE">HAHVAC::FanMode::AutoFanMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7FanMode11HighFanModeE">HAHVAC::FanMode::HighFanMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7FanMode10LowFanModeE">HAHVAC::FanMode::LowFanMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7FanMode13MediumFanModeE">HAHVAC::FanMode::MediumFanMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7FanMode14UnknownFanModeE">HAHVAC::FanMode::UnknownFanMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8FeaturesE">HAHVAC::Features (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features13ActionFeatureE">HAHVAC::Features::ActionFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features17AuxHeatingFeatureE">HAHVAC::Features::AuxHeatingFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features15DefaultFeaturesE">HAHVAC::Features::DefaultFeatures (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features10FanFeatureE">HAHVAC::Features::FanFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features12ModesFeatureE">HAHVAC::Features::ModesFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features12PowerFeatureE">HAHVAC::Features::PowerFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features12SwingFeatureE">HAHVAC::Features::SwingFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8Features24TargetTemperatureFeatureE">HAHVAC::Features::TargetTemperatureFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27getCommandWithFloatTemplateEv">HAHVAC::getCommandWithFloatTemplate (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC16getCurrentActionEv">HAHVAC::getCurrentAction (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC18getCurrentAuxStateEv">HAHVAC::getCurrentAuxState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC17getCurrentFanModeEv">HAHVAC::getCurrentFanMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC14getCurrentModeEv">HAHVAC::getCurrentMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC19getCurrentSwingModeEv">HAHVAC::getCurrentSwingMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC27getCurrentTargetTemperatureEv">HAHVAC::getCurrentTargetTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4NK6HAHVAC21getCurrentTemperatureEv">HAHVAC::getCurrentTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC6HAHVACEPKcK8uint16_tK15NumberPrecision">HAHVAC::HAHVAC (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21handleAuxStateCommandEPK7uint8_tK8uint16_t">HAHVAC::handleAuxStateCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20handleFanModeCommandEPK7uint8_tK8uint16_t">HAHVAC::handleFanModeCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC17handleModeCommandEPK7uint8_tK8uint16_t">HAHVAC::handleModeCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC18handlePowerCommandEPK7uint8_tK8uint16_t">HAHVAC::handlePowerCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC22handleSwingModeCommandEPK7uint8_tK8uint16_t">HAHVAC::handleSwingModeCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC30handleTargetTemperatureCommandEPK7uint8_tK8uint16_t">HAHVAC::handleTargetTemperatureCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4ModeE">HAHVAC::Mode (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode8AutoModeE">HAHVAC::Mode::AutoMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode8CoolModeE">HAHVAC::Mode::CoolMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode7DryModeE">HAHVAC::Mode::DryMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode11FanOnlyModeE">HAHVAC::Mode::FanOnlyMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode8HeatModeE">HAHVAC::Mode::HeatMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode7OffModeE">HAHVAC::Mode::OffMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC4Mode11UnknownModeE">HAHVAC::Mode::UnknownMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC17onAuxStateCommandEPFvbP6HAHVACE">HAHVAC::onAuxStateCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC16onFanModeCommandEPFv7FanModeP6HAHVACE">HAHVAC::onFanModeCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC13onModeCommandEPFv4ModeP6HAHVACE">HAHVAC::onModeCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15onMqttConnectedEv">HAHVAC::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC13onMqttMessageEPKcPK7uint8_tK8uint16_t">HAHVAC::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC14onPowerCommandEPFvbP6HAHVACE">HAHVAC::onPowerCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC18onSwingModeCommandEPFv9SwingModeP6HAHVACE">HAHVAC::onSwingModeCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC26onTargetTemperatureCommandEPFv9HANumericP6HAHVACE">HAHVAC::onTargetTemperatureCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC13publishActionEK6Action">HAHVAC::publishAction (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15publishAuxStateEKb">HAHVAC::publishAuxState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC25publishCurrentTemperatureERK9HANumeric">HAHVAC::publishCurrentTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC14publishFanModeEK7FanMode">HAHVAC::publishFanMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC11publishModeEK4Mode">HAHVAC::publishMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC16publishSwingModeEK9SwingMode">HAHVAC::publishSwingMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC24publishTargetTemperatureERK9HANumeric">HAHVAC::publishTargetTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9setActionEK6ActionKb">HAHVAC::setAction (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC11setAuxStateEKbKb">HAHVAC::setAuxState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC16setCurrentActionEK6Action">HAHVAC::setCurrentAction (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC18setCurrentAuxStateEKb">HAHVAC::setCurrentAuxState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEK6int8_t">HAHVAC::setCurrentCurrentTemperature (C++ function)</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEK7int16_t">[1]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEK7int32_t">[2]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEK7uint8_t">[3]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEK8uint16_t">[4]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEK8uint32_t">[5]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureEKf">[6]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC28setCurrentCurrentTemperatureERK9HANumeric">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC17setCurrentFanModeEK7FanMode">HAHVAC::setCurrentFanMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC14setCurrentModeEK4Mode">HAHVAC::setCurrentMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC19setCurrentSwingModeEK9SwingMode">HAHVAC::setCurrentSwingMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEK6int8_t">HAHVAC::setCurrentTargetTemperature (C++ function)</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEK7int16_t">[1]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEK7int32_t">[2]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEK7uint8_t">[3]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEK8uint16_t">[4]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEK8uint32_t">[5]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureEKf">[6]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC27setCurrentTargetTemperatureERK9HANumeric">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEK6int8_tKb">HAHVAC::setCurrentTemperature (C++ function)</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEK7int16_tKb">[1]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEK7int32_tKb">[2]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEK7uint8_tKb">[3]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEK8uint16_tKb">[4]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEK8uint32_tKb">[5]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureEKfKb">[6]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC21setCurrentTemperatureERK9HANumericKb">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC10setFanModeEK7FanModeKb">HAHVAC::setFanMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC11setFanModesEK7uint8_t">HAHVAC::setFanModes (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7setIconEPKc">HAHVAC::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC10setMaxTempEKf">HAHVAC::setMaxTemp (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC10setMinTempEKf">HAHVAC::setMinTemp (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC7setModeEK4ModeKb">HAHVAC::setMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC8setModesEK7uint8_t">HAHVAC::setModes (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9setRetainEKb">HAHVAC::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC12setSwingModeEK9SwingModeKb">HAHVAC::setSwingMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC13setSwingModesEK7uint8_t">HAHVAC::setSwingModes (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEK6int8_tKb">HAHVAC::setTargetTemperature (C++ function)</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEK7int16_tKb">[1]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEK7int32_tKb">[2]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEK7uint8_tKb">[3]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEK8uint16_tKb">[4]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEK8uint32_tKb">[5]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureEKfKb">[6]</a>, <a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC20setTargetTemperatureERK9HANumericKb">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC18setTemperatureUnitE15TemperatureUnit">HAHVAC::setTemperatureUnit (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC11setTempStepEKf">HAHVAC::setTempStep (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9SwingModeE">HAHVAC::SwingMode (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9SwingMode12OffSwingModeE">HAHVAC::SwingMode::OffSwingMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9SwingMode11OnSwingModeE">HAHVAC::SwingMode::OnSwingMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC9SwingMode16UnknownSwingModeE">HAHVAC::SwingMode::UnknownSwingMode (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15TemperatureUnitE">HAHVAC::TemperatureUnit (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15TemperatureUnit11CelsiusUnitE">HAHVAC::TemperatureUnit::CelsiusUnit (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15TemperatureUnit11DefaultUnitE">HAHVAC::TemperatureUnit::DefaultUnit (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVAC15TemperatureUnit14FahrenheitUnitE">HAHVAC::TemperatureUnit::FahrenheitUnit (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-hvac.html#_CPPv4N6HAHVACD0Ev">HAHVAC::~HAHVAC (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv47HALight">HALight (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight19_brightnessCallbackE">HALight::_brightnessCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight16_brightnessScaleE">HALight::_brightnessScale (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight25_colorTemperatureCallbackE">HALight::_colorTemperatureCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight18_currentBrightnessE">HALight::_currentBrightness (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight24_currentColorTemperatureE">HALight::_currentColorTemperature (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight16_currentRGBColorE">HALight::_currentRGBColor (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight13_currentStateE">HALight::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight9_featuresE">HALight::_features (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight5_iconE">HALight::_icon (C++ member)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight10_maxMiredsE">HALight::_maxMireds (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight10_minMiredsE">HALight::_minMireds (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight11_optimisticE">HALight::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight7_retainE">HALight::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight17_rgbColorCallbackE">HALight::_rgbColorCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight14_stateCallbackE">HALight::_stateCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight15buildSerializerEv">HALight::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8FeaturesE">HALight::Features (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8Features17BrightnessFeatureE">HALight::Features::BrightnessFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8Features23ColorTemperatureFeatureE">HALight::Features::ColorTemperatureFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8Features15DefaultFeaturesE">HALight::Features::DefaultFeatures (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8Features10RGBFeatureE">HALight::Features::RGBFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4NK7HALight20getCurrentBrightnessEv">HALight::getCurrentBrightness (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4NK7HALight26getCurrentColorTemperatureEv">HALight::getCurrentColorTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4NK7HALight18getCurrentRGBColorEv">HALight::getCurrentRGBColor (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4NK7HALight15getCurrentStateEv">HALight::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight7HALightEPKcK7uint8_t">HALight::HALight (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight23handleBrightnessCommandEPK7uint8_tK8uint16_t">HALight::handleBrightnessCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight29handleColorTemperatureCommandEPK7uint8_tK8uint16_t">HALight::handleColorTemperatureCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight16handleRGBCommandEPK7uint8_tK8uint16_t">HALight::handleRGBCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight18handleStateCommandEPK7uint8_tK8uint16_t">HALight::handleStateCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight19onBrightnessCommandEPFv7uint8_tP7HALightE">HALight::onBrightnessCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight25onColorTemperatureCommandEPFv8uint16_tP7HALightE">HALight::onColorTemperatureCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight15onMqttConnectedEv">HALight::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight13onMqttMessageEPKcPK7uint8_tK8uint16_t">HALight::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight17onRGBColorCommandEPFvN7HALight8RGBColorEP7HALightE">HALight::onRGBColorCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight14onStateCommandEPFvbP7HALightE">HALight::onStateCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight17publishBrightnessEK7uint8_t">HALight::publishBrightness (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight23publishColorTemperatureEK8uint16_t">HALight::publishColorTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight15publishRGBColorERK8RGBColor">HALight::publishRGBColor (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight12publishStateEKb">HALight::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColorE">HALight::RGBColor (C++ struct)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor4blueE">HALight::RGBColor::blue (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor10fromBufferEPK7uint8_tK8uint16_t">HALight::RGBColor::fromBuffer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor5greenE">HALight::RGBColor::green (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor5isSetE">HALight::RGBColor::isSet (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4NK7HALight8RGBColorneERK8RGBColor">HALight::RGBColor::operator!= (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColoraSERK8RGBColor">HALight::RGBColor::operator= (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4NK7HALight8RGBColoreqERK8RGBColor">HALight::RGBColor::operator== (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor3redE">HALight::RGBColor::red (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor8RGBColorE7uint8_t7uint8_t7uint8_t">HALight::RGBColor::RGBColor (C++ function)</a>, <a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8RGBColor8RGBColorEv">[1]</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight18RGBStringMaxLengthE">HALight::RGBStringMaxLength (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight13setBrightnessEK7uint8_tKb">HALight::setBrightness (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight18setBrightnessScaleEK7uint8_t">HALight::setBrightnessScale (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight19setColorTemperatureEK8uint16_tKb">HALight::setColorTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight20setCurrentBrightnessEK7uint8_t">HALight::setCurrentBrightness (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight26setCurrentColorTemperatureEK8uint16_t">HALight::setCurrentColorTemperature (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight18setCurrentRGBColorERK8RGBColor">HALight::setCurrentRGBColor (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight15setCurrentStateEKb">HALight::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight7setIconEPKc">HALight::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight12setMaxMiredsEK8uint16_t">HALight::setMaxMireds (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight12setMinMiredsEK8uint16_t">HALight::setMinMireds (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight13setOptimisticEKb">HALight::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight9setRetainEKb">HALight::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight11setRGBColorERK8RGBColorKb">HALight::setRGBColor (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight8setStateEKbKb">HALight::setState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight7turnOffEv">HALight::turnOff (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-light.html#_CPPv4N7HALight6turnOnEv">HALight::turnOn (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv46HALock">HALock (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock16_commandCallbackE">HALock::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock13_currentStateE">HALock::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock5_iconE">HALock::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock11_optimisticE">HALock::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock7_retainE">HALock::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock15buildSerializerEv">HALock::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4NK6HALock15getCurrentStateEv">HALock::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock6HALockEPKc">HALock::HALock (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock13handleCommandEPK7uint8_tK8uint16_t">HALock::handleCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock11LockCommandE">HALock::LockCommand (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock11LockCommand11CommandLockE">HALock::LockCommand::CommandLock (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock11LockCommand11CommandOpenE">HALock::LockCommand::CommandOpen (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock11LockCommand13CommandUnlockE">HALock::LockCommand::CommandUnlock (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock9LockStateE">HALock::LockState (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock9LockState11StateLockedE">HALock::LockState::StateLocked (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock9LockState12StateUnknownE">HALock::LockState::StateUnknown (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock9LockState13StateUnlockedE">HALock::LockState::StateUnlocked (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock9onCommandEPFv11LockCommandP6HALockE">HALock::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock15onMqttConnectedEv">HALock::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock13onMqttMessageEPKcPK7uint8_tK8uint16_t">HALock::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock12publishStateEK9LockState">HALock::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock15setCurrentStateEK9LockState">HALock::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock7setIconEPKc">HALock::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock13setOptimisticEKb">HALock::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock9setRetainEKb">HALock::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-lock.html#_CPPv4N6HALock8setStateEK9LockStateKb">HALock::setState (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv46HAMqtt">HAMqtt (C++ class)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt18_connectedCallbackE">HAMqtt::_connectedCallback (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt13_currentStateE">HAMqtt::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt11_dataPrefixE">HAMqtt::_dataPrefix (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt7_deviceE">HAMqtt::_device (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt13_devicesTypesE">HAMqtt::_devicesTypes (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15_devicesTypesNbE">HAMqtt::_devicesTypesNb (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt21_disconnectedCallbackE">HAMqtt::_disconnectedCallback (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt16_discoveryPrefixE">HAMqtt::_discoveryPrefix (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt12_initializedE">HAMqtt::_initialized (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt9_instanceE">HAMqtt::_instance (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt24_lastConnectionAttemptAtE">HAMqtt::_lastConnectionAttemptAt (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt16_lastWillMessageE">HAMqtt::_lastWillMessage (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15_lastWillRetainE">HAMqtt::_lastWillRetain (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt14_lastWillTopicE">HAMqtt::_lastWillTopic (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt18_maxDevicesTypesNbE">HAMqtt::_maxDevicesTypesNb (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt16_messageCallbackE">HAMqtt::_messageCallback (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt5_mqttE">HAMqtt::_mqtt (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt9_passwordE">HAMqtt::_password (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt21_stateChangedCallbackE">HAMqtt::_stateChangedCallback (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt9_usernameE">HAMqtt::_username (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt13addDeviceTypeEP16HABaseDeviceType">HAMqtt::addDeviceType (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt5beginEK9IPAddressK8uint16_tPKcPKc">HAMqtt::begin (C++ function)</a>, <a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt5beginEK9IPAddressPKcPKc">[1]</a>, <a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt5beginEPKcK8uint16_tPKcPKc">[2]</a>, <a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt5beginEPKcPKcPKc">[3]</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt12beginPublishEPKc8uint16_tb">HAMqtt::beginPublish (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionStateE">HAMqtt::ConnectionState (C++ enum)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState16StateBadClientIdE">HAMqtt::ConnectionState::StateBadClientId (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState19StateBadCredentialsE">HAMqtt::ConnectionState::StateBadCredentials (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState16StateBadProtocolE">HAMqtt::ConnectionState::StateBadProtocol (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState14StateConnectedE">HAMqtt::ConnectionState::StateConnected (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState15StateConnectingE">HAMqtt::ConnectionState::StateConnecting (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState21StateConnectionFailedE">HAMqtt::ConnectionState::StateConnectionFailed (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState19StateConnectionLostE">HAMqtt::ConnectionState::StateConnectionLost (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState22StateConnectionTimeoutE">HAMqtt::ConnectionState::StateConnectionTimeout (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState17StateDisconnectedE">HAMqtt::ConnectionState::StateDisconnected (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState17StateUnauthorizedE">HAMqtt::ConnectionState::StateUnauthorized (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15ConnectionState16StateUnavailableE">HAMqtt::ConnectionState::StateUnavailable (C++ enumerator)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt15connectToServerEv">HAMqtt::connectToServer (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt10disconnectEv">HAMqtt::disconnect (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt10endPublishEv">HAMqtt::endPublish (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4NK6HAMqtt13getDataPrefixEv">HAMqtt::getDataPrefix (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4NK6HAMqtt9getDeviceEv">HAMqtt::getDevice (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4NK6HAMqtt18getDiscoveryPrefixEv">HAMqtt::getDiscoveryPrefix (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4NK6HAMqtt8getStateEv">HAMqtt::getState (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt6HAMqttER6ClientR8HADeviceK7uint8_t">HAMqtt::HAMqtt (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt8instanceEv">HAMqtt::instance (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4NK6HAMqtt11isConnectedEv">HAMqtt::isConnected (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt4loopEv">HAMqtt::loop (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt11onConnectedEPFvvE">HAMqtt::onConnected (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt16onConnectedLogicEv">HAMqtt::onConnectedLogic (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt14onDisconnectedEPFvvE">HAMqtt::onDisconnected (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt9onMessageEPFvPKcPK7uint8_t8uint16_tE">HAMqtt::onMessage (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt14onStateChangedEPFv15ConnectionStateE">HAMqtt::onStateChanged (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt14processMessageEPKcPK7uint8_t8uint16_t">HAMqtt::processMessage (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt7publishEPKcPKcb">HAMqtt::publish (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt17ReconnectIntervalE">HAMqtt::ReconnectInterval (C++ member)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt13setBufferSizeE8uint16_t">HAMqtt::setBufferSize (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt13setDataPrefixEPKc">HAMqtt::setDataPrefix (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt18setDiscoveryPrefixEPKc">HAMqtt::setDiscoveryPrefix (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt12setKeepAliveE8uint16_t">HAMqtt::setKeepAlive (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt11setLastWillEPKcPKcb">HAMqtt::setLastWill (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt8setStateE15ConnectionState">HAMqtt::setState (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt9subscribeEPKc">HAMqtt::subscribe (C++ function)</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt12writePayloadEPK19__FlashStringHelper">HAMqtt::writePayload (C++ function)</a>, <a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt12writePayloadEPK7uint8_tK8uint16_t">[1]</a>, <a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqtt12writePayloadEPKcK8uint16_t">[2]</a>
</li>
      <li><a href="documents/api/core/ha-mqtt.html#_CPPv4N6HAMqttD0Ev">HAMqtt::~HAMqtt (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv48HANumber">HANumber (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber6_classE">HANumber::_class (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber16_commandCallbackE">HANumber::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber13_currentStateE">HANumber::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber5_iconE">HANumber::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber9_maxValueE">HANumber::_maxValue (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber9_minValueE">HANumber::_minValue (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber5_modeE">HANumber::_mode (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber11_optimisticE">HANumber::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber10_precisionE">HANumber::_precision (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber7_retainE">HANumber::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber5_stepE">HANumber::_step (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber18_unitOfMeasurementE">HANumber::_unitOfMeasurement (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15buildSerializerEv">HANumber::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber18getCommandTemplateEv">HANumber::getCommandTemplate (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4NK8HANumber15getCurrentStateEv">HANumber::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4NK8HANumber15getModePropertyEv">HANumber::getModeProperty (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber13handleCommandEPK7uint8_tK8uint16_t">HANumber::handleCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8HANumberEPKcK15NumberPrecision">HANumber::HANumber (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber4ModeE">HANumber::Mode (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber4Mode8ModeAutoE">HANumber::Mode::ModeAuto (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber4Mode7ModeBoxE">HANumber::Mode::ModeBox (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber4Mode10ModeSliderE">HANumber::Mode::ModeSlider (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber9onCommandEPFv9HANumericP8HANumberE">HANumber::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15onMqttConnectedEv">HANumber::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber13onMqttMessageEPKcPK7uint8_tK8uint16_t">HANumber::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber12publishStateERK9HANumeric">HANumber::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEK6int8_t">HANumber::setCurrentState (C++ function)</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEK7int16_t">[1]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEK7int32_t">[2]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEK7uint8_t">[3]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEK8uint16_t">[4]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEK8uint32_t">[5]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateEKf">[6]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber15setCurrentStateERK9HANumeric">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber14setDeviceClassEPKc">HANumber::setDeviceClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber7setIconEPKc">HANumber::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber6setMaxEKf">HANumber::setMax (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber6setMinEKf">HANumber::setMin (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber7setModeEK4Mode">HANumber::setMode (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber13setOptimisticEKb">HANumber::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber9setRetainEKb">HANumber::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEK6int8_tKb">HANumber::setState (C++ function)</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEK7int16_tKb">[1]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEK7int32_tKb">[2]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEK7uint8_tKb">[3]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEK8uint16_tKb">[4]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEK8uint32_tKb">[5]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateEKfKb">[6]</a>, <a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber8setStateERK9HANumericKb">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber7setStepEKf">HANumber::setStep (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-number.html#_CPPv4N8HANumber20setUnitOfMeasurementEPKc">HANumber::setUnitOfMeasurement (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv49HANumeric">HANumeric (C++ class)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric6_isSetE">HANumeric::_isSet (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric10_precisionE">HANumeric::_precision (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric6_valueE">HANumeric::_value (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric13calculateSizeEv">HANumeric::calculateSize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric7fromStrEPK7uint8_tK8uint16_t">HANumeric::fromStr (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric12getBaseValueEv">HANumeric::getBaseValue (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric12getPrecisionEv">HANumeric::getPrecision (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric16getPrecisionBaseEv">HANumeric::getPrecisionBase (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK6int8_tK7uint8_t">HANumeric::HANumeric (C++ function)</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK7int16_tK7uint8_t">[1]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK7int32_tK7uint8_t">[2]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK7int64_t">[3]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK7uint8_tK7uint8_t">[4]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK8uint16_tK7uint8_t">[5]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEK8uint32_tK7uint8_t">[6]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEKfK7uint8_t">[7]</a>, <a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric9HANumericEv">[8]</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7isFloatEv">HANumeric::isFloat (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7isInt16Ev">HANumeric::isInt16 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7isInt32Ev">HANumeric::isInt32 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric6isInt8Ev">HANumeric::isInt8 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric5isSetEv">HANumeric::isSet (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric8isUInt16Ev">HANumeric::isUInt16 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric8isUInt32Ev">HANumeric::isUInt32 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7isUInt8Ev">HANumeric::isUInt8 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric11MaxDigitsNbE">HANumeric::MaxDigitsNb (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumericaSERK9HANumeric">HANumeric::operator= (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumericeqERK9HANumeric">HANumeric::operator== (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric5resetEv">HANumeric::reset (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric12setBaseValueE7int64_t">HANumeric::setBaseValue (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4N9HANumeric12setPrecisionEK7uint8_t">HANumeric::setPrecision (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7toFloatEv">HANumeric::toFloat (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7toInt16Ev">HANumeric::toInt16 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7toInt32Ev">HANumeric::toInt32 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric6toInt8Ev">HANumeric::toInt8 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric5toStrEPc">HANumeric::toStr (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric8toUInt16Ev">HANumeric::toUInt16 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric8toUInt32Ev">HANumeric::toUInt32 (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-numeric.html#_CPPv4NK9HANumeric7toUInt8Ev">HANumeric::toUInt8 (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv47HAScene">HAScene (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene16_commandCallbackE">HAScene::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene5_iconE">HAScene::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene7_retainE">HAScene::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene15buildSerializerEv">HAScene::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene7HASceneEPKc">HAScene::HAScene (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene9onCommandEPFvP7HASceneE">HAScene::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene15onMqttConnectedEv">HAScene::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene13onMqttMessageEPKcPK7uint8_tK8uint16_t">HAScene::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene7setIconEPKc">HAScene::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-scene.html#_CPPv4N7HAScene9setRetainEKb">HAScene::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv48HASelect">HASelect (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect16_commandCallbackE">HASelect::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect13_currentStateE">HASelect::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect5_iconE">HASelect::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect11_optimisticE">HASelect::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect8_optionsE">HASelect::_options (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect7_retainE">HASelect::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect15buildSerializerEv">HASelect::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4NK8HASelect20countOptionsInStringEPKc">HASelect::countOptionsInString (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4NK8HASelect16getCurrentOptionEv">HASelect::getCurrentOption (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4NK8HASelect15getCurrentStateEv">HASelect::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect8HASelectEPKc">HASelect::HASelect (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect9onCommandEPFv6int8_tP8HASelectE">HASelect::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect15onMqttConnectedEv">HASelect::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect13onMqttMessageEPKcPK7uint8_tK8uint16_t">HASelect::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect12publishStateEK6int8_t">HASelect::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect15setCurrentStateEK6int8_t">HASelect::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect7setIconEPKc">HASelect::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect13setOptimisticEKb">HASelect::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect10setOptionsEPKc">HASelect::setOptions (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect9setRetainEKb">HASelect::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelect8setStateEK6int8_tKb">HASelect::setState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-select.html#_CPPv4N8HASelectD0Ev">HASelect::~HASelect (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv48HASensor">HASensor (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor12_deviceClassE">HASensor::_deviceClass (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor12_expireAfterE">HASensor::_expireAfter (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor9_featuresE">HASensor::_features (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor12_forceUpdateE">HASensor::_forceUpdate (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor5_iconE">HASensor::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor11_stateClassE">HASensor::_stateClass (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor18_unitOfMeasurementE">HASensor::_unitOfMeasurement (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor15buildSerializerEv">HASensor::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor8FeaturesE">HASensor::Features (C++ enum)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor8Features15DefaultFeaturesE">HASensor::Features::DefaultFeatures (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor8Features21JsonAttributesFeatureE">HASensor::Features::JsonAttributesFeature (C++ enumerator)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor8HASensorEPKcK8uint16_t">HASensor::HASensor (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor15onMqttConnectedEv">HASensor::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor14setDeviceClassEPKc">HASensor::setDeviceClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor14setExpireAfterE8uint16_t">HASensor::setExpireAfter (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor14setForceUpdateEb">HASensor::setForceUpdate (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor7setIconEPKc">HASensor::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor17setJsonAttributesEPKc">HASensor::setJsonAttributes (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor13setStateClassEPKc">HASensor::setStateClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor20setUnitOfMeasurementEPKc">HASensor::setUnitOfMeasurement (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor.html#_CPPv4N8HASensor8setValueEPKc">HASensor::setValue (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv414HASensorNumber">HASensorNumber (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber13_currentValueE">HASensorNumber::_currentValue (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber10_precisionE">HASensorNumber::_precision (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4NK14HASensorNumber15getCurrentValueEv">HASensorNumber::getCurrentValue (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber14HASensorNumberEPKcK15NumberPrecisionK8uint16_t">HASensorNumber::HASensorNumber (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15onMqttConnectedEv">HASensorNumber::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber12publishValueERK9HANumeric">HASensorNumber::publishValue (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEK6int8_t">HASensorNumber::setCurrentValue (C++ function)</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEK7int16_t">[1]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEK7int32_t">[2]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEK7uint8_t">[3]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEK8uint16_t">[4]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEK8uint32_t">[5]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueEKf">[6]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber15setCurrentValueERK9HANumeric">[7]</a>
</li>
      <li><a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEK6int8_tKb">HASensorNumber::setValue (C++ function)</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEK7int16_tKb">[1]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEK7int32_tKb">[2]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEK7uint8_tKb">[3]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEK8uint16_tKb">[4]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEK8uint32_tKb">[5]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueEKfKb">[6]</a>, <a href="documents/api/device-types/ha-sensor-number.html#_CPPv4N14HASensorNumber8setValueERK9HANumericKb">[7]</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv412HASerializer">HASerializer (C++ class)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer11_deviceTypeE">HASerializer::_deviceType (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer8_entriesE">HASerializer::_entries (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer10_entriesNbE">HASerializer::_entriesNb (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer13_maxEntriesNbE">HASerializer::_maxEntriesNb (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer8addEntryEv">HASerializer::addEntry (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer18calculateArraySizeEPK17HASerializerArray">HASerializer::calculateArraySize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer26calculateConfigTopicLengthEPK19__FlashStringHelperPKc">HASerializer::calculateConfigTopicLength (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer24calculateDataTopicLengthEPKcPK19__FlashStringHelper">HASerializer::calculateDataTopicLength (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer18calculateEntrySizeEPK15SerializerEntry">HASerializer::calculateEntrySize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer17calculateFlagSizeEK8FlagType">HASerializer::calculateFlagSize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer26calculatePropertyValueSizeEPK15SerializerEntry">HASerializer::calculatePropertyValueSize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer13calculateSizeEv">HASerializer::calculateSize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer23calculateTopicEntrySizeEPK15SerializerEntry">HASerializer::calculateTopicEntrySize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17compareDataTopicsEPKcPKcPK19__FlashStringHelper">HASerializer::compareDataTopics (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer9EntryTypeE">HASerializer::EntryType (C++ enum)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer9EntryType13FlagEntryTypeE">HASerializer::EntryType::FlagEntryType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer9EntryType17PropertyEntryTypeE">HASerializer::EntryType::PropertyEntryType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer9EntryType14TopicEntryTypeE">HASerializer::EntryType::TopicEntryType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer9EntryType16UnknownEntryTypeE">HASerializer::EntryType::UnknownEntryType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer8FlagTypeE">HASerializer::FlagType (C++ enum)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer8FlagType16WithAvailabilityE">HASerializer::FlagType::WithAvailability (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer8FlagType10WithDeviceE">HASerializer::FlagType::WithDevice (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer8FlagType12WithUniqueIdE">HASerializer::FlagType::WithUniqueId (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer5flushEv">HASerializer::flush (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer10flushEntryEPK15SerializerEntry">HASerializer::flushEntry (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer15flushEntryValueEPK15SerializerEntry">HASerializer::flushEntryValue (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer9flushFlagEPK15SerializerEntry">HASerializer::flushFlag (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer10flushTopicEPK15SerializerEntry">HASerializer::flushTopic (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer19generateConfigTopicEPcPK19__FlashStringHelperPKc">HASerializer::generateConfigTopic (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17generateDataTopicEPcPKcPK19__FlashStringHelper">HASerializer::generateDataTopic (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer10getEntriesEv">HASerializer::getEntries (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4NK12HASerializer12getEntriesNbEv">HASerializer::getEntriesNb (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer12HASerializerEP16HABaseDeviceTypeK7uint8_t">HASerializer::HASerializer (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueTypeE">HASerializer::PropertyValueType (C++ enum)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueType17ArrayPropertyTypeE">HASerializer::PropertyValueType::ArrayPropertyType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueType16BoolPropertyTypeE">HASerializer::PropertyValueType::BoolPropertyType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueType22ConstCharPropertyValueE">HASerializer::PropertyValueType::ConstCharPropertyValue (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueType18NumberPropertyTypeE">HASerializer::PropertyValueType::NumberPropertyType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueType20ProgmemPropertyValueE">HASerializer::PropertyValueType::ProgmemPropertyValue (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer17PropertyValueType24UnknownPropertyValueTypeE">HASerializer::PropertyValueType::UnknownPropertyValueType (C++ enumerator)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer15SerializerEntryE">HASerializer::SerializerEntry (C++ struct)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer15SerializerEntry8propertyE">HASerializer::SerializerEntry::property (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer15SerializerEntry15SerializerEntryEv">HASerializer::SerializerEntry::SerializerEntry (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer15SerializerEntry7subtypeE">HASerializer::SerializerEntry::subtype (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer15SerializerEntry4typeE">HASerializer::SerializerEntry::type (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer15SerializerEntry5valueE">HASerializer::SerializerEntry::value (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer3setEK8FlagType">HASerializer::set (C++ function)</a>, <a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer3setEPK19__FlashStringHelperPKv17PropertyValueType">[1]</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializer5topicEPK19__FlashStringHelper">HASerializer::topic (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer.html#_CPPv4N12HASerializerD0Ev">HASerializer::~HASerializer (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv417HASerializerArray">HASerializerArray (C++ class)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray6_itemsE">HASerializerArray::_items (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray8_itemsNbE">HASerializerArray::_itemsNb (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray13_progmemItemsE">HASerializerArray::_progmemItems (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray5_sizeE">HASerializerArray::_size (C++ member)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray3addE8ItemType">HASerializerArray::add (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4NK17HASerializerArray13calculateSizeEv">HASerializerArray::calculateSize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray5clearEv">HASerializerArray::clear (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4NK17HASerializerArray7getItemEK7uint8_t">HASerializerArray::getItem (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4NK17HASerializerArray8getItemsEv">HASerializerArray::getItems (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4NK17HASerializerArray10getItemsNbEv">HASerializerArray::getItemsNb (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray17HASerializerArrayEK7uint8_tKb">HASerializerArray::HASerializerArray (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArray8ItemTypeE">HASerializerArray::ItemType (C++ type)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4NK17HASerializerArray9serializeEPc">HASerializerArray::serialize (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-serializer-array.html#_CPPv4N17HASerializerArrayD0Ev">HASerializerArray::~HASerializerArray (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv48HASwitch">HASwitch (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch6_classE">HASwitch::_class (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch16_commandCallbackE">HASwitch::_commandCallback (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch13_currentStateE">HASwitch::_currentState (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch5_iconE">HASwitch::_icon (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch11_optimisticE">HASwitch::_optimistic (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch7_retainE">HASwitch::_retain (C++ member)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch15buildSerializerEv">HASwitch::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4NK8HASwitch15getCurrentStateEv">HASwitch::getCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch8HASwitchEPKc">HASwitch::HASwitch (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch9onCommandEPFvbP8HASwitchE">HASwitch::onCommand (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch15onMqttConnectedEv">HASwitch::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch13onMqttMessageEPKcPK7uint8_tK8uint16_t">HASwitch::onMqttMessage (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch12publishStateEKb">HASwitch::publishState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch15setCurrentStateEKb">HASwitch::setCurrentState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch14setDeviceClassEPKc">HASwitch::setDeviceClass (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch7setIconEPKc">HASwitch::setIcon (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch13setOptimisticEKb">HASwitch::setOptimistic (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch9setRetainEKb">HASwitch::setRetain (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch8setStateEKbKb">HASwitch::setState (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch7turnOffEv">HASwitch::turnOff (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-switch.html#_CPPv4N8HASwitch6turnOnEv">HASwitch::turnOn (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-tag-scanner.html#_CPPv412HATagScanner">HATagScanner (C++ class)</a>
</li>
      <li><a href="documents/api/device-types/ha-tag-scanner.html#_CPPv4N12HATagScanner15buildSerializerEv">HATagScanner::buildSerializer (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-tag-scanner.html#_CPPv4N12HATagScanner12HATagScannerEPKc">HATagScanner::HATagScanner (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-tag-scanner.html#_CPPv4N12HATagScanner15onMqttConnectedEv">HATagScanner::onMqttConnected (C++ function)</a>
</li>
      <li><a href="documents/api/device-types/ha-tag-scanner.html#_CPPv4N12HATagScanner10tagScannedEPKc">HATagScanner::tagScanned (C++ function)</a>
</li>
      <li><a href="documents/api/utils/ha-utils.html#_CPPv47HAUtils">HAUtils (C++ class)</a>
</li>
      <li><a href="documents/api/utils/ha-utils.html#_CPPv4N7HAUtils14byteArrayToStrEPK4byteK8uint16_t">HAUtils::byteArrayToStr (C++ function)</a>, <a href="documents/api/utils/ha-utils.html#_CPPv4N7HAUtils14byteArrayToStrEPcPK4byteK8uint16_t">[1]</a>
</li>
      <li><a href="documents/api/utils/ha-utils.html#_CPPv4N7HAUtils8endsWithEPKcPKc">HAUtils::endsWith (C++ function)</a>
</li>
  </ul></td>
</tr></table>



          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>