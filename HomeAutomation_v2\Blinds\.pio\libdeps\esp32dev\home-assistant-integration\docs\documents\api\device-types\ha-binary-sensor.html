<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>HABinarySensor class - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../../_static/theme-vendors.js"></script> -->
      <script src="../../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../../genindex.html" />
  <link rel="search" title="Search" href="../../../search.html" />
  <link rel="next" title="HAButton class" href="ha-button.html" />
  <link rel="prev" title="HABaseDeviceType class" href="ha-base-device-type.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">API reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../core/index.html">Core API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="../index.html">API reference</a> &raquo;</li>
    
      <li><a href="index.html">Device types API</a> &raquo;</li>
    
    <li>HABinarySensor class</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="ha-base-device-type.html"
       title="previous chapter">← HABaseDeviceType class</a>
  </li>
  <li class="next">
    <a href="ha-button.html"
       title="next chapter">HAButton class →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="habinarysensor-class">
<h1>HABinarySensor class<a class="headerlink" href="#habinarysensor-class" title="Permalink to this headline">¶</a></h1>
<dl class="cpp class">
<dt class="sig sig-object cpp" id="_CPPv414HABinarySensor">
<span id="_CPPv314HABinarySensor"></span><span id="_CPPv214HABinarySensor"></span><span id="HABinarySensor"></span><span class="target" id="class_h_a_binary_sensor"></span><span class="k"><span class="pre">class</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">HABinarySensor</span></span></span><span class="w"> </span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="k"><span class="pre">public</span></span><span class="w"> </span><a class="reference internal" href="ha-base-device-type.html#_CPPv416HABaseDeviceType" title="HABaseDeviceType"><span class="n"><span class="pre">HABaseDeviceType</span></span></a><a class="headerlink" href="#_CPPv414HABinarySensor" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="#class_h_a_binary_sensor"><span class="std std-ref">HABinarySensor</span></a> represents a binary sensor that allows publishing on/off state to the Home Assistant panel.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You can find more information about this entity in the Home Assistant documentation: <a class="reference external" href="https://www.home-assistant.io/integrations/binary_sensor.mqtt/">https://www.home-assistant.io/integrations/binary_sensor.mqtt/</a> </p>
</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-functions">Public Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor14HABinarySensorEPKc">
<span id="_CPPv3N14HABinarySensor14HABinarySensorEPKc"></span><span id="_CPPv2N14HABinarySensor14HABinarySensorEPKc"></span><span id="HABinarySensor::HABinarySensor__cCP"></span><span class="target" id="class_h_a_binary_sensor_1a52b939597d19f117515ae4c515b6be5a"></span><span class="sig-name descname"><span class="n"><span class="pre">HABinarySensor</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">uniqueId</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor14HABinarySensorEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>uniqueId</strong> – The unique ID of the button. It needs to be unique in a scope of your device. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor8setStateEKbKb">
<span id="_CPPv3N14HABinarySensor8setStateEKbKb"></span><span id="_CPPv2N14HABinarySensor8setStateEKbKb"></span><span id="HABinarySensor::setState__bC.bC"></span><span class="target" id="class_h_a_binary_sensor_1a137016ed9a25100de2503160e2c285c3"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">force</span></span><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="k"><span class="pre">false</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor8setStateEKbKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Changes state of the sensor and publish MQTT message. Please note that if a new value is the same as the previous one the MQTT message won’t be published.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>state</strong> – New state of the sensor (<code class="docutils literal notranslate"><span class="pre">true</span></code> - on, <code class="docutils literal notranslate"><span class="pre">false</span></code> - off). </p></li>
<li><p><strong>force</strong> – Forces to update the state without comparing it to a previous known state. </p></li>
</ul>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor14setExpireAfterE8uint16_t">
<span id="_CPPv3N14HABinarySensor14setExpireAfterE8uint16_t"></span><span id="_CPPv2N14HABinarySensor14setExpireAfterE8uint16_t"></span><span id="HABinarySensor::setExpireAfter__uint16_t"></span><span class="target" id="class_h_a_binary_sensor_1a684beba9239222e44e998f8baea41da4"></span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setExpireAfter</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">uint16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">expireAfter</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor14setExpireAfterE8uint16_t" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets the number of seconds after the sensor’s state expires, if it’s not updated. By default the sensors state never expires.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>expireAfter</strong> – The number of seconds. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor15setCurrentStateEKb">
<span id="_CPPv3N14HABinarySensor15setCurrentStateEKb"></span><span id="_CPPv2N14HABinarySensor15setCurrentStateEKb"></span><span id="HABinarySensor::setCurrentState__bC"></span><span class="target" id="class_h_a_binary_sensor_1a04aa233b543a5d08559de0b772331ced"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setCurrentState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor15setCurrentStateEKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets the current state of the sensor without publishing it to Home Assistant. This method may be useful if you want to change the state before the connection with the MQTT broker is acquired.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>state</strong> – New state of the sensor. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK14HABinarySensor15getCurrentStateEv">
<span id="_CPPv3NK14HABinarySensor15getCurrentStateEv"></span><span id="_CPPv2NK14HABinarySensor15getCurrentStateEv"></span><span id="HABinarySensor::getCurrentStateC"></span><span class="target" id="class_h_a_binary_sensor_1a01ce95f5ffe68b9baeb4b31383ef3efc"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">getCurrentState</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK14HABinarySensor15getCurrentStateEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns the last known state of the sensor. </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor14setDeviceClassEPKc">
<span id="_CPPv3N14HABinarySensor14setDeviceClassEPKc"></span><span id="_CPPv2N14HABinarySensor14setDeviceClassEPKc"></span><span id="HABinarySensor::setDeviceClass__cCP"></span><span class="target" id="class_h_a_binary_sensor_1afe64445b11b3f30ab6c140ee876e472d"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setDeviceClass</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">deviceClass</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor14setDeviceClassEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets class of the device. You can find list of available values here: <a class="reference external" href="https://www.home-assistant.io/integrations/binary_sensor/#device-class">https://www.home-assistant.io/integrations/binary_sensor/#device-class</a></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>deviceClass</strong> – The class name. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor7setIconEPKc">
<span id="_CPPv3N14HABinarySensor7setIconEPKc"></span><span id="_CPPv2N14HABinarySensor7setIconEPKc"></span><span id="HABinarySensor::setIcon__cCP"></span><span class="target" id="class_h_a_binary_sensor_1ab21b355dd9b19f9f594f12fdf26b4f05"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setIcon</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">icon</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor7setIconEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets icon of the sensor. Any icon from MaterialDesignIcons.com (for example: <code class="docutils literal notranslate"><span class="pre">mdi:home</span></code>).</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>icon</strong> – The icon name. </p>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-protected-functions">Protected Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor15buildSerializerEv">
<span id="_CPPv3N14HABinarySensor15buildSerializerEv"></span><span id="_CPPv2N14HABinarySensor15buildSerializerEv"></span><span id="HABinarySensor::buildSerializer"></span><span class="target" id="class_h_a_binary_sensor_1ab153632bd88a4650549ca24ae7cdc655"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buildSerializer</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N14HABinarySensor15buildSerializerEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor15onMqttConnectedEv">
<span id="_CPPv3N14HABinarySensor15onMqttConnectedEv"></span><span id="_CPPv2N14HABinarySensor15onMqttConnectedEv"></span><span id="HABinarySensor::onMqttConnected"></span><span class="target" id="class_h_a_binary_sensor_1a0db4269ce7239d86eb472b7b87e7f620"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttConnected</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N14HABinarySensor15onMqttConnectedEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-functions">Private Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor12publishStateEb">
<span id="_CPPv3N14HABinarySensor12publishStateEb"></span><span id="_CPPv2N14HABinarySensor12publishStateEb"></span><span id="HABinarySensor::publishState__b"></span><span class="target" id="class_h_a_binary_sensor_1a96ca417b9001501b876a60e477dce953"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">publishState</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N14HABinarySensor12publishStateEb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Publishes the MQTT message with the given state.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>state</strong> – The state to publish. </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-members">Private Members</p>
<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor6_classE">
<span id="_CPPv3N14HABinarySensor6_classE"></span><span id="_CPPv2N14HABinarySensor6_classE"></span><span id="HABinarySensor::_class__cCP"></span><span class="target" id="class_h_a_binary_sensor_1af670394be65514a99f94f7ad689f0ac8"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_class</span></span></span><a class="headerlink" href="#_CPPv4N14HABinarySensor6_classE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The device class. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor5_iconE">
<span id="_CPPv3N14HABinarySensor5_iconE"></span><span id="_CPPv2N14HABinarySensor5_iconE"></span><span id="HABinarySensor::_icon__cCP"></span><span class="target" id="class_h_a_binary_sensor_1ab828fefdcca4e68c951fad0b49177398"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_icon</span></span></span><a class="headerlink" href="#_CPPv4N14HABinarySensor5_iconE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The icon of the sensor. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor12_expireAfterE">
<span id="_CPPv3N14HABinarySensor12_expireAfterE"></span><span id="_CPPv2N14HABinarySensor12_expireAfterE"></span><span id="HABinarySensor::_expireAfter__HANumeric"></span><span class="target" id="class_h_a_binary_sensor_1a644e21f52a160a702c3124070722315a"></span><a class="reference internal" href="../utils/ha-numeric.html#_CPPv49HANumeric" title="HANumeric"><span class="n"><span class="pre">HANumeric</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_expireAfter</span></span></span><a class="headerlink" href="#_CPPv4N14HABinarySensor12_expireAfterE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>It defines the number of seconds after the sensor’s state expires, if it’s not updated. By default the sensors state never expires. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N14HABinarySensor13_currentStateE">
<span id="_CPPv3N14HABinarySensor13_currentStateE"></span><span id="_CPPv2N14HABinarySensor13_currentStateE"></span><span id="HABinarySensor::_currentState__b"></span><span class="target" id="class_h_a_binary_sensor_1a107a5872cf1d9788fe35895278dad79e"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_currentState</span></span></span><a class="headerlink" href="#_CPPv4N14HABinarySensor13_currentStateE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Current state of the sensor. By default it’s false. </p>
</dd></dl>

</div>
</dd></dl>

</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="ha-base-device-type.html"
       title="previous chapter">← HABaseDeviceType class</a>
  </li>
  <li class="next">
    <a href="ha-button.html"
       title="next chapter">HAButton class →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>