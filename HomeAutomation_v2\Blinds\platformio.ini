; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32@^6.5.0
board = esp32dev
framework = arduino

monitor_speed = 115200

; change MCU frequency = 80 MHz (min for WIFI is 80)
board_build.f_cpu = 160000000L

lib_deps =  ayushsharma82/AsyncElegantOTA@^2.2.7
            https://github.com/me-no-dev/ESPAsyncWebServer.git
            https://github.com/dawi<PERSON><PERSON><PERSON><PERSON><PERSON>/arduino-home-assistant.git
            https://github.com/sui77/rc-switch.git

    
build_flags = -DCORE_DEBUG_LEVEL=5