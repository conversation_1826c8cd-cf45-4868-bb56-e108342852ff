#include "MotorController.h"
#include <Arduino.h>

MotorController::MotorController(int motorId, int stepPin, int dirPin, int enablePin)
    : motorId(motorId), stepPin(stepPin), dirPin(dirPin), enablePin(enablePin),
      targetPosition(0), currentPosition(0), maxPosition(30000), isEnabled(false),
      lastStepTime(0), preferences() {
  pinMode(stepPin, OUTPUT);
  pinMode(dirPin, OUTPUT);
  pinMode(enablePin, OUTPUT);
  digitalWrite(enablePin, HIGH); // Disable motor by default
}

void MotorController::begin() {

  char nameSpace[8];
  sprintf(nameSpace, "motor_%d", motorId);

  preferences.begin(nameSpace, false);  // Initialize Preferences with namespace "app"

  // Load stored position and max position from Preferences
  setCurrentPosition(preferences.getInt("currentPosition", 0));

  // Check if stored maxPosition is less than 30,000, update it
  int storedMaxPosition = preferences.getInt("maxPosition", 30000);
  setMaxPosition(storedMaxPosition);  // Use stored maxPosition

  //Update home assistant
  haveUpdate = true;
}

void MotorController::enable() {
  digitalWrite(enablePin, LOW);
  isEnabled = true;
}

void MotorController::disable() {
  saveCurrentPositionToPreferences(); // Save current position before disabling
  digitalWrite(enablePin, HIGH);
  isEnabled = false;
  haveUpdate = true;
}

void MotorController::goToPosition(int targetPos) {
  // Clamp target position between 0 and maxPosition
  targetPosition = constrain(targetPos, 0, maxPosition);
  enable();
  // Set the direction pin based on the target position
  digitalWrite(dirPin, (targetPosition > currentPosition) ? HIGH : LOW);
}

void MotorController::open() {
  goToPosition(0);
}

void MotorController::stop() {
  disable();
}

void MotorController::close() {
  goToPosition(maxPosition);
}


void MotorController::update() {
  if (!isEnabled) {
    return; // Motor is disabled, nothing to do
  }

  unsigned long currentTime = micros();

  // Check if enough time has passed since the last step
  int default_step_time = 3000;

  if(targetPosition > currentPosition) {
    //Going down
    //We can go down faster
    default_step_time = 1500;
  }


  if (currentTime - lastStepTime >= default_step_time) {
    digitalWrite(stepPin, HIGH);
    lastStepTime = currentTime;

    //@100rpm - 1 pulse(2us) every 3ms
    //@150rpm - 1 pulse(2us) every 2ms
    //@300rpm - 1 pulse(2us) every 1ms


    // Add a small delay (3 microseconds) for raising the step signal
    delayMicroseconds(3);

    digitalWrite(stepPin, LOW);

    // Update the current position if the step is taken
    if (currentPosition != targetPosition) {
      currentPosition += (targetPosition > currentPosition) ? 1 : -1;
    }
  }

  // Check if the target position is reached
  if (currentPosition == targetPosition) {
    disable(); // Disable motor when the target position is reached
  }
}

void MotorController::setCurrentPosition(int position) {
  currentPosition = position;
}

int MotorController::getCurrentPositionNormalized() {
  return 100 - floor(((currentPosition * 1.0) / ( maxPosition * 1.0 )) * 100);
}

int MotorController::getCurrentPosition() const {
  return currentPosition;
}

void MotorController::setMaxPosition(int maxPos) {
  maxPosition = maxPos;
  preferences.putInt("maxPosition", maxPosition);
  //preferences.end();
}

int MotorController::getMaxPosition() {
  return maxPosition;
}

bool MotorController::getIsEnabled() {
    return isEnabled;
}

void MotorController::resetPosition() {
  setCurrentPosition(0);
}

void MotorController::saveCurrentPositionToPreferences() {
  // Save current position to Preferences
  preferences.putInt("currentPosition", currentPosition);
  //preferences.end();
}


bool MotorController::getHaveUpdate() {
  return haveUpdate;
}

void MotorController::clearHaveUpdate() {
  haveUpdate = false;
}