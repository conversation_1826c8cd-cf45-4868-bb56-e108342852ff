<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>HADeviceTracker class - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../../_static/theme-vendors.js"></script> -->
      <script src="../../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../../genindex.html" />
  <link rel="search" title="Search" href="../../../search.html" />
  <link rel="next" title="HADeviceTrigger class" href="ha-device-trigger.html" />
  <link rel="prev" title="HACover class" href="ha-cover.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">API reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../core/index.html">Core API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="../index.html">API reference</a> &raquo;</li>
    
      <li><a href="index.html">Device types API</a> &raquo;</li>
    
    <li>HADeviceTracker class</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="ha-cover.html"
       title="previous chapter">← HACover class</a>
  </li>
  <li class="next">
    <a href="ha-device-trigger.html"
       title="next chapter">HADeviceTrigger class →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="hadevicetracker-class">
<h1>HADeviceTracker class<a class="headerlink" href="#hadevicetracker-class" title="Permalink to this headline">¶</a></h1>
<dl class="cpp class">
<dt class="sig sig-object cpp" id="_CPPv415HADeviceTracker">
<span id="_CPPv315HADeviceTracker"></span><span id="_CPPv215HADeviceTracker"></span><span id="HADeviceTracker"></span><span class="target" id="class_h_a_device_tracker"></span><span class="k"><span class="pre">class</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTracker</span></span></span><span class="w"> </span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="k"><span class="pre">public</span></span><span class="w"> </span><a class="reference internal" href="ha-base-device-type.html#_CPPv416HABaseDeviceType" title="HABaseDeviceType"><span class="n"><span class="pre">HABaseDeviceType</span></span></a><a class="headerlink" href="#_CPPv415HADeviceTracker" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="#class_h_a_device_tracker"><span class="std std-ref">HADeviceTracker</span></a> allows to implement a custom device’s tracker.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You can find more information about this entity in the Home Assistant documentation: <a class="reference external" href="https://www.home-assistant.io/integrations/device_tracker.mqtt/">https://www.home-assistant.io/integrations/device_tracker.mqtt/</a> </p>
</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-types">Public Types</p>
<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker10SourceTypeE">
<span id="_CPPv3N15HADeviceTracker10SourceTypeE"></span><span id="_CPPv2N15HADeviceTracker10SourceTypeE"></span><span class="target" id="class_h_a_device_tracker_1a38a338bbdb1b7cf37092e1fefaef6c6f"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">SourceType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker10SourceTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Available source types of the tracker. </p>
<p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker10SourceType17SourceTypeUnknownE">
<span id="_CPPv3N15HADeviceTracker10SourceType17SourceTypeUnknownE"></span><span id="_CPPv2N15HADeviceTracker10SourceType17SourceTypeUnknownE"></span><span class="target" id="class_h_a_device_tracker_1a38a338bbdb1b7cf37092e1fefaef6c6fac0a09ef1c187b102b0435fc650b3fcbe"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">SourceTypeUnknown</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker10SourceType17SourceTypeUnknownE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker10SourceType13SourceTypeGPSE">
<span id="_CPPv3N15HADeviceTracker10SourceType13SourceTypeGPSE"></span><span id="_CPPv2N15HADeviceTracker10SourceType13SourceTypeGPSE"></span><span class="target" id="class_h_a_device_tracker_1a38a338bbdb1b7cf37092e1fefaef6c6fa60a8a4aa8d8ae4d6e453d169d32c1871"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">SourceTypeGPS</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker10SourceType13SourceTypeGPSE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker10SourceType16SourceTypeRouterE">
<span id="_CPPv3N15HADeviceTracker10SourceType16SourceTypeRouterE"></span><span id="_CPPv2N15HADeviceTracker10SourceType16SourceTypeRouterE"></span><span class="target" id="class_h_a_device_tracker_1a38a338bbdb1b7cf37092e1fefaef6c6fa42ff1741c0461a5afd8f526c1a872ed5"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">SourceTypeRouter</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker10SourceType16SourceTypeRouterE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker10SourceType19SourceTypeBluetoothE">
<span id="_CPPv3N15HADeviceTracker10SourceType19SourceTypeBluetoothE"></span><span id="_CPPv2N15HADeviceTracker10SourceType19SourceTypeBluetoothE"></span><span class="target" id="class_h_a_device_tracker_1a38a338bbdb1b7cf37092e1fefaef6c6faa53035502cf00b9f26c9968ea0d3d897"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">SourceTypeBluetooth</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker10SourceType19SourceTypeBluetoothE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker10SourceType21SourceTypeBluetoothLEE">
<span id="_CPPv3N15HADeviceTracker10SourceType21SourceTypeBluetoothLEE"></span><span id="_CPPv2N15HADeviceTracker10SourceType21SourceTypeBluetoothLEE"></span><span class="target" id="class_h_a_device_tracker_1a38a338bbdb1b7cf37092e1fefaef6c6fa6fb61fe863b9a18d992a27a73115bf39"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">SourceTypeBluetoothLE</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker10SourceType21SourceTypeBluetoothLEE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker12TrackerStateE">
<span id="_CPPv3N15HADeviceTracker12TrackerStateE"></span><span id="_CPPv2N15HADeviceTracker12TrackerStateE"></span><span class="target" id="class_h_a_device_tracker_1a984e77906c38849770bfc9893b607bfe"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">TrackerState</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker12TrackerStateE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Available states that can be reported to the HA panel. </p>
<p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker12TrackerState12StateUnknownE">
<span id="_CPPv3N15HADeviceTracker12TrackerState12StateUnknownE"></span><span id="_CPPv2N15HADeviceTracker12TrackerState12StateUnknownE"></span><span class="target" id="class_h_a_device_tracker_1a984e77906c38849770bfc9893b607bfeab4411e307b821590b20a457b28e94404"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateUnknown</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker12TrackerState12StateUnknownE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker12TrackerState9StateHomeE">
<span id="_CPPv3N15HADeviceTracker12TrackerState9StateHomeE"></span><span id="_CPPv2N15HADeviceTracker12TrackerState9StateHomeE"></span><span class="target" id="class_h_a_device_tracker_1a984e77906c38849770bfc9893b607bfea98cab4bf2c8b7f766c578844c871e9fd"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateHome</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker12TrackerState9StateHomeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker12TrackerState12StateNotHomeE">
<span id="_CPPv3N15HADeviceTracker12TrackerState12StateNotHomeE"></span><span id="_CPPv2N15HADeviceTracker12TrackerState12StateNotHomeE"></span><span class="target" id="class_h_a_device_tracker_1a984e77906c38849770bfc9893b607bfeabfe0d8076e8567535903887ced3b9628"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateNotHome</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker12TrackerState12StateNotHomeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker12TrackerState17StateNotAvailableE">
<span id="_CPPv3N15HADeviceTracker12TrackerState17StateNotAvailableE"></span><span id="_CPPv2N15HADeviceTracker12TrackerState17StateNotAvailableE"></span><span class="target" id="class_h_a_device_tracker_1a984e77906c38849770bfc9893b607bfeacd717223d3b368f35dba02ce1c7c9416"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateNotAvailable</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker12TrackerState17StateNotAvailableE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-functions">Public Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker15HADeviceTrackerEPKc">
<span id="_CPPv3N15HADeviceTracker15HADeviceTrackerEPKc"></span><span id="_CPPv2N15HADeviceTracker15HADeviceTrackerEPKc"></span><span id="HADeviceTracker::HADeviceTracker__cCP"></span><span class="target" id="class_h_a_device_tracker_1ad93d8a008fc52e8dce708b0d538f51af"></span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTracker</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">uniqueId</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTracker15HADeviceTrackerEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>uniqueId</strong> – The unique ID of the tracker. It needs to be unique in a scope of your device. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker8setStateEK12TrackerStateKb">
<span id="_CPPv3N15HADeviceTracker8setStateEK12TrackerStateKb"></span><span id="_CPPv2N15HADeviceTracker8setStateEK12TrackerStateKb"></span><span id="HADeviceTracker::setState__TrackerStateC.bC"></span><span class="target" id="class_h_a_device_tracker_1ac36ef94bb91c143301f35ecec2f47b66"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N15HADeviceTracker12TrackerStateE" title="HADeviceTracker::TrackerState"><span class="n"><span class="pre">TrackerState</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">force</span></span><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="k"><span class="pre">false</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTracker8setStateEK12TrackerStateKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Changes the state of the tracker and publishes MQTT message. Please note that if a new value is the same as previous one, the MQTT message won’t be published.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>state</strong> – The new state of the tracker. </p></li>
<li><p><strong>force</strong> – Forces to update the state without comparing it to a previous known state. </p></li>
</ul>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker15setCurrentStateEK12TrackerState">
<span id="_CPPv3N15HADeviceTracker15setCurrentStateEK12TrackerState"></span><span id="_CPPv2N15HADeviceTracker15setCurrentStateEK12TrackerState"></span><span id="HADeviceTracker::setCurrentState__TrackerStateC"></span><span class="target" id="class_h_a_device_tracker_1a58e49bf4027e3cb9e4f45177d4ed2bb0"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setCurrentState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N15HADeviceTracker12TrackerStateE" title="HADeviceTracker::TrackerState"><span class="n"><span class="pre">TrackerState</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTracker15setCurrentStateEK12TrackerState" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets the current state of the tracker without publishing it to Home Assistant. This method may be useful if you want to change the state before connection with MQTT broker is acquired.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>state</strong> – The new state of the tracker. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTracker8getStateEv">
<span id="_CPPv3NK15HADeviceTracker8getStateEv"></span><span id="_CPPv2NK15HADeviceTracker8getStateEv"></span><span id="HADeviceTracker::getStateC"></span><span class="target" id="class_h_a_device_tracker_1a385aaea54a1facc2d01db59bf7a63a88"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N15HADeviceTracker12TrackerStateE" title="HADeviceTracker::TrackerState"><span class="n"><span class="pre">TrackerState</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">getState</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTracker8getStateEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns the last known state of the tracker. If setState method wasn’t called the initial value will be returned. </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker7setIconEPKc">
<span id="_CPPv3N15HADeviceTracker7setIconEPKc"></span><span id="_CPPv2N15HADeviceTracker7setIconEPKc"></span><span id="HADeviceTracker::setIcon__cCP"></span><span class="target" id="class_h_a_device_tracker_1a727faee54b475aae5351afe7b3e72bf9"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setIcon</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">icon</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTracker7setIconEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets icon of the tracker. Any icon from MaterialDesignIcons.com (for example: <code class="docutils literal notranslate"><span class="pre">mdi:home</span></code>).</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>icon</strong> – The icon name. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker13setSourceTypeEK10SourceType">
<span id="_CPPv3N15HADeviceTracker13setSourceTypeEK10SourceType"></span><span id="_CPPv2N15HADeviceTracker13setSourceTypeEK10SourceType"></span><span id="HADeviceTracker::setSourceType__SourceTypeC"></span><span class="target" id="class_h_a_device_tracker_1ac5ae4dfb5dea532dc3b891524f6ceb62"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setSourceType</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N15HADeviceTracker10SourceTypeE" title="HADeviceTracker::SourceType"><span class="n"><span class="pre">SourceType</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTracker13setSourceTypeEK10SourceType" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets the source type of the tracker.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>type</strong> – The source type (gps, router, bluetooth, bluetooth LE). </p>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-protected-functions">Protected Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker15buildSerializerEv">
<span id="_CPPv3N15HADeviceTracker15buildSerializerEv"></span><span id="_CPPv2N15HADeviceTracker15buildSerializerEv"></span><span id="HADeviceTracker::buildSerializer"></span><span class="target" id="class_h_a_device_tracker_1ad45fcf9a201312aaea4b9c263569343b"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buildSerializer</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker15buildSerializerEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker15onMqttConnectedEv">
<span id="_CPPv3N15HADeviceTracker15onMqttConnectedEv"></span><span id="_CPPv2N15HADeviceTracker15onMqttConnectedEv"></span><span id="HADeviceTracker::onMqttConnected"></span><span class="target" id="class_h_a_device_tracker_1a87ad38b8132d8bf7c42885b785c72fd8"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttConnected</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker15onMqttConnectedEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-functions">Private Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker12publishStateE12TrackerState">
<span id="_CPPv3N15HADeviceTracker12publishStateE12TrackerState"></span><span id="_CPPv2N15HADeviceTracker12publishStateE12TrackerState"></span><span id="HADeviceTracker::publishState__TrackerState"></span><span class="target" id="class_h_a_device_tracker_1ae3e5c8aa3e12702052fc65badc4fb7c2"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">publishState</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#_CPPv4N15HADeviceTracker12TrackerStateE" title="HADeviceTracker::TrackerState"><span class="n"><span class="pre">TrackerState</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTracker12publishStateE12TrackerState" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Publishes the MQTT message with the given state.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>state</strong> – The state to publish. </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTracker21getSourceTypePropertyEv">
<span id="_CPPv3NK15HADeviceTracker21getSourceTypePropertyEv"></span><span id="_CPPv2NK15HADeviceTracker21getSourceTypePropertyEv"></span><span id="HADeviceTracker::getSourceTypePropertyC"></span><span class="target" id="class_h_a_device_tracker_1a66da7ce02358a637b65bddb03a9b9bf9"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">__FlashStringHelper</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getSourceTypeProperty</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTracker21getSourceTypePropertyEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns progmem string representing source type of the tracker. </p>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-members">Private Members</p>
<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker5_iconE">
<span id="_CPPv3N15HADeviceTracker5_iconE"></span><span id="_CPPv2N15HADeviceTracker5_iconE"></span><span id="HADeviceTracker::_icon__cCP"></span><span class="target" id="class_h_a_device_tracker_1a6c828c6f05a8aaafb547a48b87607cb8"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_icon</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker5_iconE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The icon of the tracker. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker11_sourceTypeE">
<span id="_CPPv3N15HADeviceTracker11_sourceTypeE"></span><span id="_CPPv2N15HADeviceTracker11_sourceTypeE"></span><span id="HADeviceTracker::_sourceType__SourceType"></span><span class="target" id="class_h_a_device_tracker_1a3692d1f199246a3f49ff2f3cfd93ff2c"></span><a class="reference internal" href="#_CPPv4N15HADeviceTracker10SourceTypeE" title="HADeviceTracker::SourceType"><span class="n"><span class="pre">SourceType</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_sourceType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker11_sourceTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The source type of the tracker. By default it’s <code class="docutils literal notranslate"><span class="pre">HADeviceTracker::SourceTypeUnknown</span></code>. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTracker13_currentStateE">
<span id="_CPPv3N15HADeviceTracker13_currentStateE"></span><span id="_CPPv2N15HADeviceTracker13_currentStateE"></span><span id="HADeviceTracker::_currentState__TrackerState"></span><span class="target" id="class_h_a_device_tracker_1a7ec99b3627fffe1467cb330361a02f56"></span><a class="reference internal" href="#_CPPv4N15HADeviceTracker12TrackerStateE" title="HADeviceTracker::TrackerState"><span class="n"><span class="pre">TrackerState</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_currentState</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTracker13_currentStateE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The current state of the device’s tracker. By default its <code class="docutils literal notranslate"><span class="pre">HADeviceTracker::StateUnknown</span></code>. </p>
</dd></dl>

</div>
</dd></dl>

</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="ha-cover.html"
       title="previous chapter">← HACover class</a>
  </li>
  <li class="next">
    <a href="ha-device-trigger.html"
       title="next chapter">HADeviceTrigger class →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>