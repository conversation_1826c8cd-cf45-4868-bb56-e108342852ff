Compatible Hardware
===================

The library uses the Arduino Ethernet Client API for interacting with the network hardware.
It should work fine as long as the `Client` class is available.

Here is the list of devices on which the library was tested:

- Arduino Uno
- Arduino Mega
- Arduino Nano
- Arduino Pro Mini
- Arduino Nano 33 IoT
- Arduino Due
- NodeMCU
- Controllino Mega (Pure)
- Controllino Maxi (Pure)
- ESP-01
- ESP32-CAM
- Sonoff Dual R2
- Sonoff Dual R3
- Sonoff Basic
- Sonoff Mini
- Tuya Wi-Fi switch module
- Tuya Wi-Fi curtain module

Please note that it's not the complete list of supported devices.
You may try to use the library on any device that uses Arduino core.