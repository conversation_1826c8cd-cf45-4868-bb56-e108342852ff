<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>Device configuration - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="Availability reporting" href="availability-reporting.html" />
  <link rel="prev" title="Introduction" href="introduction.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Library</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="introduction.html">Introduction</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="index.html">Library</a> &raquo;</li>
    
    <li>Device configuration</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="introduction.html"
       title="previous chapter">← Introduction</a>
  </li>
  <li class="next">
    <a href="availability-reporting.html"
       title="next chapter">Availability reporting →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="device-configuration">
<h1>Device configuration<a class="headerlink" href="#device-configuration" title="Permalink to this headline">¶</a></h1>
<p><a class="reference internal" href="../api/core/ha-device.html"><span class="doc">HADevice</span></a> represents the physical device on which the library is used.
Essentially, it’s a group of types such as sensors, switches, lights, and more.
Within Home Assistant, it appears with properties that can be configured using the library’s API.</p>
<p>Every property, except for the unique ID, is optional.
Enabling optional properties can lead to increased flash and RAM usage, therefore it is not advisable to set them on lower-spec MCUs.</p>
<p>The supported properties are:</p>
<ul class="simple">
<li><p>unique ID*</p></li>
<li><p>name</p></li>
<li><p>software version</p></li>
<li><p>manufacturer</p></li>
<li><p>model</p></li>
<li><p>configuration url</p></li>
</ul>
<section id="unique-id">
<h2>Unique ID<a class="headerlink" href="#unique-id" title="Permalink to this headline">¶</a></h2>
<p>The unique ID serves as an internal identifier for devices within Home Assistant.
With this ID, Home Assistant can monitor the device’s parameters and the entities it exposes.
The unique ID must be distinct within the scope of the Home Assistant instance.
The recommended approach is to use the MAC address of an Ethernet or Wi-Fi chip.</p>
<p>There are three distinct methods for setting the device ID, allowing you to choose the one that best suits your requirements.</p>
<section id="providing-string-const-char-to-the-hadevice-constructor">
<h3>1) Providing string (const char*) to the <a class="reference internal" href="../api/core/ha-device.html"><span class="doc">HADevice</span></a> constructor<a class="headerlink" href="#providing-string-const-char-to-the-hadevice-constructor" title="Permalink to this headline">¶</a></h3>
<p>Try to keep the ID simple (alphanumeric characters) and short.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="s">&quot;myUniqueID&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="providing-byte-array-to-the-hadevice-constructor">
<h3>2) Providing byte array to the <a class="reference internal" href="../api/core/ha-device.html"><span class="doc">HADevice</span></a> constructor<a class="headerlink" href="#providing-byte-array-to-the-hadevice-constructor" title="Permalink to this headline">¶</a></h3>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="c1">// use your own unique bytes sequence</span>
<span class="n">byte</span><span class="w"> </span><span class="n">mac</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mh">0x00</span><span class="p">,</span><span class="w"> </span><span class="mh">0x10</span><span class="p">,</span><span class="w"> </span><span class="mh">0xFA</span><span class="p">,</span><span class="w"> </span><span class="mh">0x6E</span><span class="p">,</span><span class="w"> </span><span class="mh">0x38</span><span class="p">,</span><span class="w"> </span><span class="mh">0x4A</span><span class="p">};</span><span class="w"></span>
<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="n">mac</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">mac</span><span class="p">));</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="using-hadevice-setuniqueid-method-during-the-setup">
<h3>3) Using <a class="reference internal" href="../api/core/ha-device.html"><span class="doc">HADevice::setUniqueId</span></a> method during the setup<a class="headerlink" href="#using-hadevice-setuniqueid-method-during-the-setup" title="Permalink to this headline">¶</a></h3>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">HADevice</span><span class="w"> </span><span class="n">device</span><span class="p">;</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// any length is acceptable</span>
<span class="w">    </span><span class="n">byte</span><span class="w"> </span><span class="n">myId</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mh">0x05</span><span class="p">,</span><span class="w"> </span><span class="mh">0xb4</span><span class="p">,</span><span class="w"> </span><span class="mh">0xc6</span><span class="p">,</span><span class="w"> </span><span class="mh">0x9f</span><span class="p">,</span><span class="w"> </span><span class="mh">0xbe</span><span class="p">,</span><span class="w"> </span><span class="mh">0xce</span><span class="p">,</span><span class="w"> </span><span class="mh">0x8c</span><span class="p">,</span><span class="w"> </span><span class="mh">0x1f</span><span class="p">,</span><span class="w"> </span><span class="mh">0xc7</span><span class="p">};</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">setUniqueId</span><span class="p">(</span><span class="n">myId</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">myId</span><span class="p">));</span><span class="w"> </span><span class="c1">// the input array is cloned internally</span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="device-properties">
<h2>Device properties<a class="headerlink" href="#device-properties" title="Permalink to this headline">¶</a></h2>
<p>Each property has its corresponding setter method in the <a class="reference internal" href="../api/core/ha-device.html"><span class="doc">HADevice</span></a> class.
Please note that all of these methods accept a const char pointer whose <strong>content is not copied</strong>.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="s">&quot;myUniqueId&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">setName</span><span class="p">(</span><span class="s">&quot;Bedroom Light Controller&quot;</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">setSoftwareVersion</span><span class="p">(</span><span class="s">&quot;1.0.0&quot;</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">setManufacturer</span><span class="p">(</span><span class="s">&quot;Developer Corp.&quot;</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">setModel</span><span class="p">(</span><span class="s">&quot;ABC-123&quot;</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">setConfigurationUrl</span><span class="p">(</span><span class="s">&quot;http://192.168.1.55:1234&quot;</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="introduction.html"
       title="previous chapter">← Introduction</a>
  </li>
  <li class="next">
    <a href="availability-reporting.html"
       title="next chapter">Availability reporting →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>