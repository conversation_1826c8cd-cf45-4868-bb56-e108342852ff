pre { line-height: 125%; }
td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.highlight .hll { background-color: #4f424c }
.highlight { background: #2f1e2e; color: #e7e9db }
.highlight .c { color: #776e71 } /* Comment */
.highlight .err { color: #ef6155 } /* Error */
.highlight .k { color: #815ba4 } /* Keyword */
.highlight .l { color: #f99b15 } /* Literal */
.highlight .n { color: #e7e9db } /* Name */
.highlight .o { color: #5bc4bf } /* Operator */
.highlight .p { color: #e7e9db } /* Punctuation */
.highlight .ch { color: #776e71 } /* Comment.Hashbang */
.highlight .cm { color: #776e71 } /* Comment.Multiline */
.highlight .cp { color: #776e71 } /* Comment.Preproc */
.highlight .cpf { color: #776e71 } /* Comment.PreprocFile */
.highlight .c1 { color: #776e71 } /* Comment.Single */
.highlight .cs { color: #776e71 } /* Comment.Special */
.highlight .gd { color: #ef6155 } /* Generic.Deleted */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .gh { color: #e7e9db; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #48b685 } /* Generic.Inserted */
.highlight .gp { color: #776e71; font-weight: bold } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #5bc4bf; font-weight: bold } /* Generic.Subheading */
.highlight .kc { color: #815ba4 } /* Keyword.Constant */
.highlight .kd { color: #815ba4 } /* Keyword.Declaration */
.highlight .kn { color: #5bc4bf } /* Keyword.Namespace */
.highlight .kp { color: #815ba4 } /* Keyword.Pseudo */
.highlight .kr { color: #815ba4 } /* Keyword.Reserved */
.highlight .kt { color: #fec418 } /* Keyword.Type */
.highlight .ld { color: #48b685 } /* Literal.Date */
.highlight .m { color: #f99b15 } /* Literal.Number */
.highlight .s { color: #48b685 } /* Literal.String */
.highlight .na { color: #06b6ef } /* Name.Attribute */
.highlight .nb { color: #e7e9db } /* Name.Builtin */
.highlight .nc { color: #fec418 } /* Name.Class */
.highlight .no { color: #ef6155 } /* Name.Constant */
.highlight .nd { color: #5bc4bf } /* Name.Decorator */
.highlight .ni { color: #e7e9db } /* Name.Entity */
.highlight .ne { color: #ef6155 } /* Name.Exception */
.highlight .nf { color: #06b6ef } /* Name.Function */
.highlight .nl { color: #e7e9db } /* Name.Label */
.highlight .nn { color: #fec418 } /* Name.Namespace */
.highlight .nx { color: #06b6ef } /* Name.Other */
.highlight .py { color: #e7e9db } /* Name.Property */
.highlight .nt { color: #5bc4bf } /* Name.Tag */
.highlight .nv { color: #ef6155 } /* Name.Variable */
.highlight .ow { color: #5bc4bf } /* Operator.Word */
.highlight .w { color: #e7e9db } /* Text.Whitespace */
.highlight .mb { color: #f99b15 } /* Literal.Number.Bin */
.highlight .mf { color: #f99b15 } /* Literal.Number.Float */
.highlight .mh { color: #f99b15 } /* Literal.Number.Hex */
.highlight .mi { color: #f99b15 } /* Literal.Number.Integer */
.highlight .mo { color: #f99b15 } /* Literal.Number.Oct */
.highlight .sa { color: #48b685 } /* Literal.String.Affix */
.highlight .sb { color: #48b685 } /* Literal.String.Backtick */
.highlight .sc { color: #e7e9db } /* Literal.String.Char */
.highlight .dl { color: #48b685 } /* Literal.String.Delimiter */
.highlight .sd { color: #776e71 } /* Literal.String.Doc */
.highlight .s2 { color: #48b685 } /* Literal.String.Double */
.highlight .se { color: #f99b15 } /* Literal.String.Escape */
.highlight .sh { color: #48b685 } /* Literal.String.Heredoc */
.highlight .si { color: #f99b15 } /* Literal.String.Interpol */
.highlight .sx { color: #48b685 } /* Literal.String.Other */
.highlight .sr { color: #48b685 } /* Literal.String.Regex */
.highlight .s1 { color: #48b685 } /* Literal.String.Single */
.highlight .ss { color: #48b685 } /* Literal.String.Symbol */
.highlight .bp { color: #e7e9db } /* Name.Builtin.Pseudo */
.highlight .fm { color: #06b6ef } /* Name.Function.Magic */
.highlight .vc { color: #ef6155 } /* Name.Variable.Class */
.highlight .vg { color: #ef6155 } /* Name.Variable.Global */
.highlight .vi { color: #ef6155 } /* Name.Variable.Instance */
.highlight .vm { color: #ef6155 } /* Name.Variable.Magic */
.highlight .il { color: #f99b15 } /* Literal.Number.Integer.Long */