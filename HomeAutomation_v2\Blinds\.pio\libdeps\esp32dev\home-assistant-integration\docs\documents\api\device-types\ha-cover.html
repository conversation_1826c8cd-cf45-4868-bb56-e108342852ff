<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>HACover class - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../../_static/theme-vendors.js"></script> -->
      <script src="../../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../../genindex.html" />
  <link rel="search" title="Search" href="../../../search.html" />
  <link rel="next" title="HADeviceTracker class" href="ha-device-tracker.html" />
  <link rel="prev" title="HACamera class" href="ha-camera.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">API reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../core/index.html">Core API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="../index.html">API reference</a> &raquo;</li>
    
      <li><a href="index.html">Device types API</a> &raquo;</li>
    
    <li>HACover class</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="ha-camera.html"
       title="previous chapter">← HACamera class</a>
  </li>
  <li class="next">
    <a href="ha-device-tracker.html"
       title="next chapter">HADeviceTracker class →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="hacover-class">
<h1>HACover class<a class="headerlink" href="#hacover-class" title="Permalink to this headline">¶</a></h1>
<dl class="cpp class">
<dt class="sig sig-object cpp" id="_CPPv47HACover">
<span id="_CPPv37HACover"></span><span id="_CPPv27HACover"></span><span id="HACover"></span><span class="target" id="class_h_a_cover"></span><span class="k"><span class="pre">class</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">HACover</span></span></span><span class="w"> </span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="k"><span class="pre">public</span></span><span class="w"> </span><a class="reference internal" href="ha-base-device-type.html#_CPPv416HABaseDeviceType" title="HABaseDeviceType"><span class="n"><span class="pre">HABaseDeviceType</span></span></a><a class="headerlink" href="#_CPPv47HACover" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="#class_h_a_cover"><span class="std std-ref">HACover</span></a> allows to control a cover (such as blinds, a roller shutter or a garage door).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You can find more information about this entity in the Home Assistant documentation: <a class="reference external" href="https://www.home-assistant.io/integrations/cover.mqtt/">https://www.home-assistant.io/integrations/cover.mqtt/</a> </p>
</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-types">Public Types</p>
<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverStateE">
<span id="_CPPv3N7HACover10CoverStateE"></span><span id="_CPPv2N7HACover10CoverStateE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">CoverState</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverStateE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverState12StateUnknownE">
<span id="_CPPv3N7HACover10CoverState12StateUnknownE"></span><span id="_CPPv2N7HACover10CoverState12StateUnknownE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3a0bb2002080264aef2eb0eeb90f2a4342"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateUnknown</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverState12StateUnknownE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverState11StateClosedE">
<span id="_CPPv3N7HACover10CoverState11StateClosedE"></span><span id="_CPPv2N7HACover10CoverState11StateClosedE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3acc2f1726d4034308b43cd2fe82f1fc11"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateClosed</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverState11StateClosedE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverState12StateClosingE">
<span id="_CPPv3N7HACover10CoverState12StateClosingE"></span><span id="_CPPv2N7HACover10CoverState12StateClosingE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3a8efcc3f5000d7c4133cbb8b9af8625fe"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateClosing</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverState12StateClosingE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverState9StateOpenE">
<span id="_CPPv3N7HACover10CoverState9StateOpenE"></span><span id="_CPPv2N7HACover10CoverState9StateOpenE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3a54c11f902fc670938b5259f4a068f1c2"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateOpen</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverState9StateOpenE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverState12StateOpeningE">
<span id="_CPPv3N7HACover10CoverState12StateOpeningE"></span><span id="_CPPv2N7HACover10CoverState12StateOpeningE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3ace9c0422c7667d39acb8ea99069ddb79"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateOpening</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverState12StateOpeningE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover10CoverState12StateStoppedE">
<span id="_CPPv3N7HACover10CoverState12StateStoppedE"></span><span id="_CPPv2N7HACover10CoverState12StateStoppedE"></span><span class="target" id="class_h_a_cover_1ad229c0964c1d47f91d1306234f5b5cf3aee74d67d0123d0ffe2d20c5f70de1884"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">StateStopped</span></span></span><a class="headerlink" href="#_CPPv4N7HACover10CoverState12StateStoppedE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover12CoverCommandE">
<span id="_CPPv3N7HACover12CoverCommandE"></span><span id="_CPPv2N7HACover12CoverCommandE"></span><span class="target" id="class_h_a_cover_1a72c09efa117126e78797eb2a3c5bb153"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">CoverCommand</span></span></span><a class="headerlink" href="#_CPPv4N7HACover12CoverCommandE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover12CoverCommand11CommandOpenE">
<span id="_CPPv3N7HACover12CoverCommand11CommandOpenE"></span><span id="_CPPv2N7HACover12CoverCommand11CommandOpenE"></span><span class="target" id="class_h_a_cover_1a72c09efa117126e78797eb2a3c5bb153a63e10091905d1aaf92962438e2ddc03f"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">CommandOpen</span></span></span><a class="headerlink" href="#_CPPv4N7HACover12CoverCommand11CommandOpenE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover12CoverCommand12CommandCloseE">
<span id="_CPPv3N7HACover12CoverCommand12CommandCloseE"></span><span id="_CPPv2N7HACover12CoverCommand12CommandCloseE"></span><span class="target" id="class_h_a_cover_1a72c09efa117126e78797eb2a3c5bb153a97325817f0f938904b759bad9df6c196"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">CommandClose</span></span></span><a class="headerlink" href="#_CPPv4N7HACover12CoverCommand12CommandCloseE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover12CoverCommand11CommandStopE">
<span id="_CPPv3N7HACover12CoverCommand11CommandStopE"></span><span id="_CPPv2N7HACover12CoverCommand11CommandStopE"></span><span class="target" id="class_h_a_cover_1a72c09efa117126e78797eb2a3c5bb153aabf3c7f4d1be4143ff86be9aa033789e"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">CommandStop</span></span></span><a class="headerlink" href="#_CPPv4N7HACover12CoverCommand11CommandStopE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover8FeaturesE">
<span id="_CPPv3N7HACover8FeaturesE"></span><span id="_CPPv2N7HACover8FeaturesE"></span><span class="target" id="class_h_a_cover_1ab349728070a8155b5e324e262164bdcc"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Features</span></span></span><a class="headerlink" href="#_CPPv4N7HACover8FeaturesE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover8Features15DefaultFeaturesE">
<span id="_CPPv3N7HACover8Features15DefaultFeaturesE"></span><span id="_CPPv2N7HACover8Features15DefaultFeaturesE"></span><span class="target" id="class_h_a_cover_1ab349728070a8155b5e324e262164bdcca842f269795f95b472dd650e681aa9d52"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">DefaultFeatures</span></span></span><a class="headerlink" href="#_CPPv4N7HACover8Features15DefaultFeaturesE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover8Features15PositionFeatureE">
<span id="_CPPv3N7HACover8Features15PositionFeatureE"></span><span id="_CPPv2N7HACover8Features15PositionFeatureE"></span><span class="target" id="class_h_a_cover_1ab349728070a8155b5e324e262164bdccad9486dafd6b99659a46aea97bfef3ce6"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PositionFeature</span></span></span><a class="headerlink" href="#_CPPv4N7HACover8Features15PositionFeatureE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-functions">Public Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover7HACoverEPKcK8Features">
<span id="_CPPv3N7HACover7HACoverEPKcK8Features"></span><span id="_CPPv2N7HACover7HACoverEPKcK8Features"></span><span id="HACover::HACover__cCP.FeaturesC"></span><span class="target" id="class_h_a_cover_1a734701c0938c9e8853f87645ba8a6278"></span><span class="sig-name descname"><span class="n"><span class="pre">HACover</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">uniqueId</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N7HACover8FeaturesE" title="HACover::Features"><span class="n"><span class="pre">Features</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">features</span></span><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N7HACover8Features15DefaultFeaturesE" title="HACover::DefaultFeatures"><span class="n"><span class="pre">DefaultFeatures</span></span></a><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover7HACoverEPKcK8Features" title="Permalink to this definition">¶</a><br /></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>uniqueId</strong> – The unique ID of the cover. It needs to be unique in a scope of your device. </p></li>
<li><p><strong>features</strong> – Features that should be enabled for the fan. </p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover8setStateEK10CoverStateKb">
<span id="_CPPv3N7HACover8setStateEK10CoverStateKb"></span><span id="_CPPv2N7HACover8setStateEK10CoverStateKb"></span><span id="HACover::setState__CoverStateC.bC"></span><span class="target" id="class_h_a_cover_1a951136b667d9ef2e3d89516c306258ee"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N7HACover10CoverStateE" title="HACover::CoverState"><span class="n"><span class="pre">CoverState</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">force</span></span><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="k"><span class="pre">false</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover8setStateEK10CoverStateKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Changes state of the cover and publishes MQTT message. Please note that if a new value is the same as previous one, the MQTT message won’t be published.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>state</strong> – New state of the cover. </p></li>
<li><p><strong>force</strong> – Forces to update state without comparing it to previous known state. </p></li>
</ul>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns true if MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover11setPositionEK7int16_tKb">
<span id="_CPPv3N7HACover11setPositionEK7int16_tKb"></span><span id="_CPPv2N7HACover11setPositionEK7int16_tKb"></span><span id="HACover::setPosition__int16_tC.bC"></span><span class="target" id="class_h_a_cover_1ac187b7e96c542810fd20fd825a7c9c76"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setPosition</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">int16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">position</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">force</span></span><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="k"><span class="pre">false</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover11setPositionEK7int16_tKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Changes the position of the cover and publishes MQTT message. Please note that if a new value is the same as previous one, the MQTT message won’t be published.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>position</strong> – The new position of the cover (0-100). </p></li>
<li><p><strong>force</strong> – Forces to update the state without comparing it to a previous known state. </p></li>
</ul>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover15setCurrentStateEK10CoverState">
<span id="_CPPv3N7HACover15setCurrentStateEK10CoverState"></span><span id="_CPPv2N7HACover15setCurrentStateEK10CoverState"></span><span id="HACover::setCurrentState__CoverStateC"></span><span class="target" id="class_h_a_cover_1a9ac315467ca1d777d60185d10ccb583e"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setCurrentState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N7HACover10CoverStateE" title="HACover::CoverState"><span class="n"><span class="pre">CoverState</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover15setCurrentStateEK10CoverState" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets the current state of the cover without publishing it to Home Assistant. This method may be useful if you want to change the state before the connection with the MQTT broker is acquired.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>state</strong> – The new state of the cover. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK7HACover15getCurrentStateEv">
<span id="_CPPv3NK7HACover15getCurrentStateEv"></span><span id="_CPPv2NK7HACover15getCurrentStateEv"></span><span id="HACover::getCurrentStateC"></span><span class="target" id="class_h_a_cover_1a3622be83282b1d3d00ddde58213fd1d5"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N7HACover10CoverStateE" title="HACover::CoverState"><span class="n"><span class="pre">CoverState</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">getCurrentState</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK7HACover15getCurrentStateEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns last known state of the cover. By default the state is set to CoverState::StateUnknown </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover18setCurrentPositionEK7int16_t">
<span id="_CPPv3N7HACover18setCurrentPositionEK7int16_t"></span><span id="_CPPv2N7HACover18setCurrentPositionEK7int16_t"></span><span id="HACover::setCurrentPosition__int16_tC"></span><span class="target" id="class_h_a_cover_1af5197b9cf6ca7960b5457669ab425a71"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setCurrentPosition</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">int16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">position</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover18setCurrentPositionEK7int16_t" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets the current position of the cover without pushing the value to Home Assistant. This method may be useful if you want to change the position before the connection with the MQTT broker is acquired.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>position</strong> – The new position of the cover (0-100). </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK7HACover18getCurrentPositionEv">
<span id="_CPPv3NK7HACover18getCurrentPositionEv"></span><span id="_CPPv2NK7HACover18getCurrentPositionEv"></span><span id="HACover::getCurrentPositionC"></span><span class="target" id="class_h_a_cover_1ae1cfe3fee21730a5a53fc549fdcdb32e"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="n"><span class="pre">int16_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">getCurrentPosition</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK7HACover18getCurrentPositionEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns the last known position of the cover. By default position is set to HACover::DefaultPosition </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover14setDeviceClassEPKc">
<span id="_CPPv3N7HACover14setDeviceClassEPKc"></span><span id="_CPPv2N7HACover14setDeviceClassEPKc"></span><span id="HACover::setDeviceClass__cCP"></span><span class="target" id="class_h_a_cover_1a3d0b05d67d4b97c0faf616f84bd2ad5b"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setDeviceClass</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">deviceClass</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover14setDeviceClassEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets class of the device. You can find list of available values here: <a class="reference external" href="https://www.home-assistant.io/integrations/cover/">https://www.home-assistant.io/integrations/cover/</a></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>deviceClass</strong> – The class name. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover7setIconEPKc">
<span id="_CPPv3N7HACover7setIconEPKc"></span><span id="_CPPv2N7HACover7setIconEPKc"></span><span id="HACover::setIcon__cCP"></span><span class="target" id="class_h_a_cover_1addcc10a098f7af8ffbc01663c709e128"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setIcon</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">icon</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover7setIconEPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets icon of the cover. Any icon from MaterialDesignIcons.com (for example: <code class="docutils literal notranslate"><span class="pre">mdi:home</span></code>).</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>icon</strong> – The icon name. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover9setRetainEKb">
<span id="_CPPv3N7HACover9setRetainEKb"></span><span id="_CPPv2N7HACover9setRetainEKb"></span><span id="HACover::setRetain__bC"></span><span class="target" id="class_h_a_cover_1a38622ab040876086d96d96c8d8e8b68c"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setRetain</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">retain</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover9setRetainEKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets retain flag for the cover’s command. If set to <code class="docutils literal notranslate"><span class="pre">true</span></code> the command produced by Home Assistant will be retained.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>retain</strong> – </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover13setOptimisticEKb">
<span id="_CPPv3N7HACover13setOptimisticEKb"></span><span id="_CPPv2N7HACover13setOptimisticEKb"></span><span id="HACover::setOptimistic__bC"></span><span class="target" id="class_h_a_cover_1a42a4565a985e763ecaed98501c57af31"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">setOptimistic</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">optimistic</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover13setOptimisticEKb" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Sets optimistic flag for the cover state. In this mode the cover state doesn’t need to be reported back to the HA panel when a command is received. By default the optimistic mode is disabled.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>optimistic</strong> – The optimistic mode (<code class="docutils literal notranslate"><span class="pre">true</span></code> - enabled, <code class="docutils literal notranslate"><span class="pre">false</span></code> - disabled). </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover9onCommandEPFv12CoverCommandP7HACoverE">
<span id="_CPPv3N7HACover9onCommandEPFv12CoverCommandP7HACoverE"></span><span id="_CPPv2N7HACover9onCommandEPFv12CoverCommandP7HACoverE"></span><span class="target" id="class_h_a_cover_1abbb83662728bb1b9c2ef12eec56ebaab"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onCommand</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">callback</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#_CPPv4N7HACover12CoverCommandE" title="HACover::CoverCommand"><span class="n"><span class="pre">CoverCommand</span></span></a><span class="w"> </span><span class="n"><span class="pre">cmd</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv47HACover" title="HACover"><span class="n"><span class="pre">HACover</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">sender</span></span><span class="p"><span class="pre">)</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover9onCommandEPFv12CoverCommandP7HACoverE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Registers callback that will be called each time the command from HA is received. Please note that it’s not possible to register multiple callbacks for the same cover.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>callback</strong> – </p>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-static-attributes">Public Static Attributes</p>
<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover15DefaultPositionE">
<span id="_CPPv3N7HACover15DefaultPositionE"></span><span id="_CPPv2N7HACover15DefaultPositionE"></span><span id="HACover::DefaultPosition__int16_tC"></span><span class="target" id="class_h_a_cover_1afd4d58fb2330cfca558aee2c4fec9902"></span><span class="k"><span class="pre">static</span></span><span class="w"> </span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">int16_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">DefaultPosition</span></span></span><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="o"><span class="pre">-</span></span><span class="m"><span class="pre">32768</span></span><a class="headerlink" href="#_CPPv4N7HACover15DefaultPositionE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-protected-functions">Protected Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover15buildSerializerEv">
<span id="_CPPv3N7HACover15buildSerializerEv"></span><span id="_CPPv2N7HACover15buildSerializerEv"></span><span id="HACover::buildSerializer"></span><span class="target" id="class_h_a_cover_1a3570934f50a3e49ded5bf5503447473a"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buildSerializer</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N7HACover15buildSerializerEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover15onMqttConnectedEv">
<span id="_CPPv3N7HACover15onMqttConnectedEv"></span><span id="_CPPv2N7HACover15onMqttConnectedEv"></span><span id="HACover::onMqttConnected"></span><span class="target" id="class_h_a_cover_1a33425e7e7c0d47401d8d6d15786a0903"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttConnected</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N7HACover15onMqttConnectedEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover13onMqttMessageEPKcPK7uint8_tK8uint16_t">
<span id="_CPPv3N7HACover13onMqttMessageEPKcPK7uint8_tK8uint16_t"></span><span id="_CPPv2N7HACover13onMqttMessageEPKcPK7uint8_tK8uint16_t"></span><span id="HACover::onMqttMessage__cCP.uint8_tCP.uint16_tC"></span><span class="target" id="class_h_a_cover_1a9e8375cb30d742cee84cb4bbae72c2e5"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttMessage</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">topic</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint8_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">payload</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">length</span></span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N7HACover13onMqttMessageEPKcPK7uint8_tK8uint16_t" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-functions">Private Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover12publishStateEK10CoverState">
<span id="_CPPv3N7HACover12publishStateEK10CoverState"></span><span id="_CPPv2N7HACover12publishStateEK10CoverState"></span><span id="HACover::publishState__CoverStateC"></span><span class="target" id="class_h_a_cover_1ad3ff2ed975f0150b3cfa6256bb66642a"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">publishState</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv4N7HACover10CoverStateE" title="HACover::CoverState"><span class="n"><span class="pre">CoverState</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">state</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover12publishStateEK10CoverState" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Publishes the MQTT message with the given state.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>state</strong> – The state to publish. </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover15publishPositionEK7int16_t">
<span id="_CPPv3N7HACover15publishPositionEK7int16_t"></span><span id="_CPPv2N7HACover15publishPositionEK7int16_t"></span><span id="HACover::publishPosition__int16_tC"></span><span class="target" id="class_h_a_cover_1a8f526e5ef26d8dfc5e20cfea43673de9"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">publishPosition</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">int16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">position</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover15publishPositionEK7int16_t" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Publishes the MQTT message with the given position.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>position</strong> – The position to publish. </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover13handleCommandEPK7uint8_tK8uint16_t">
<span id="_CPPv3N7HACover13handleCommandEPK7uint8_tK8uint16_t"></span><span id="_CPPv2N7HACover13handleCommandEPK7uint8_tK8uint16_t"></span><span id="HACover::handleCommand__uint8_tCP.uint16_tC"></span><span class="target" id="class_h_a_cover_1a290b1a2e4c5cc15d145701dd7804118d"></span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">handleCommand</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint8_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">cmd</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint16_t</span></span><span class="w"> </span><span class="n sig-param"><span class="pre">length</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N7HACover13handleCommandEPK7uint8_tK8uint16_t" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Parses the given command and executes the cover’s callback with proper enum’s property.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>cmd</strong> – The data of the command. </p></li>
<li><p><strong>length</strong> – Length of the command. </p></li>
</ul>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-members">Private Members</p>
<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover9_featuresE">
<span id="_CPPv3N7HACover9_featuresE"></span><span id="_CPPv2N7HACover9_featuresE"></span><span id="HACover::_features__uint8_tC"></span><span class="target" id="class_h_a_cover_1a3e458afa9f7be9a312a5a7e22679ea18"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint8_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_features</span></span></span><a class="headerlink" href="#_CPPv4N7HACover9_featuresE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Features enabled for the cover. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover13_currentStateE">
<span id="_CPPv3N7HACover13_currentStateE"></span><span id="_CPPv2N7HACover13_currentStateE"></span><span id="HACover::_currentState__CoverState"></span><span class="target" id="class_h_a_cover_1a1ebddaf7dc9e916708c7db64d4db5b47"></span><a class="reference internal" href="#_CPPv4N7HACover10CoverStateE" title="HACover::CoverState"><span class="n"><span class="pre">CoverState</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_currentState</span></span></span><a class="headerlink" href="#_CPPv4N7HACover13_currentStateE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The current state of the cover. By default it’s <code class="docutils literal notranslate"><span class="pre">HACover::StateUnknown</span></code>. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover16_currentPositionE">
<span id="_CPPv3N7HACover16_currentPositionE"></span><span id="_CPPv2N7HACover16_currentPositionE"></span><span id="HACover::_currentPosition__int16_t"></span><span class="target" id="class_h_a_cover_1ad68c05ebfa36c614ef5d775857495b5d"></span><span class="n"><span class="pre">int16_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_currentPosition</span></span></span><a class="headerlink" href="#_CPPv4N7HACover16_currentPositionE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The current position of the cover. By default it’s <code class="docutils literal notranslate"><span class="pre">HACover::DefaultPosition</span></code>. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover6_classE">
<span id="_CPPv3N7HACover6_classE"></span><span id="_CPPv2N7HACover6_classE"></span><span id="HACover::_class__cCP"></span><span class="target" id="class_h_a_cover_1ae252c5c6534f7c07cc81cafcaf94e907"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_class</span></span></span><a class="headerlink" href="#_CPPv4N7HACover6_classE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The device class. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover5_iconE">
<span id="_CPPv3N7HACover5_iconE"></span><span id="_CPPv2N7HACover5_iconE"></span><span id="HACover::_icon__cCP"></span><span class="target" id="class_h_a_cover_1a7c5d27c9c5cd882d36564dae3d84939a"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_icon</span></span></span><a class="headerlink" href="#_CPPv4N7HACover5_iconE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The icon of the button. It can be nullptr. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover7_retainE">
<span id="_CPPv3N7HACover7_retainE"></span><span id="_CPPv2N7HACover7_retainE"></span><span id="HACover::_retain__b"></span><span class="target" id="class_h_a_cover_1ad159e7eefbcfcb2040da810b8444102f"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_retain</span></span></span><a class="headerlink" href="#_CPPv4N7HACover7_retainE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The retain flag for the HA commands. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover11_optimisticE">
<span id="_CPPv3N7HACover11_optimisticE"></span><span id="_CPPv2N7HACover11_optimisticE"></span><span id="HACover::_optimistic__b"></span><span class="target" id="class_h_a_cover_1a93ff94c4c40fd06e627c5c1884f45d3a"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_optimistic</span></span></span><a class="headerlink" href="#_CPPv4N7HACover11_optimisticE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The optimistic mode of the cover (<code class="docutils literal notranslate"><span class="pre">true</span></code> - enabled, <code class="docutils literal notranslate"><span class="pre">false</span></code> - disabled). </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N7HACover16_commandCallbackE">
<span id="_CPPv3N7HACover16_commandCallbackE"></span><span id="_CPPv2N7HACover16_commandCallbackE"></span><span class="target" id="class_h_a_cover_1a8a3466f0350ae8d5ce9607c36aae4509"></span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_commandCallback</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#_CPPv4N7HACover12CoverCommandE" title="HACover::CoverCommand"><span class="n"><span class="pre">CoverCommand</span></span></a><span class="w"> </span><span class="n"><span class="pre">cmd</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="#_CPPv47HACover" title="HACover"><span class="n"><span class="pre">HACover</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">sender</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#_CPPv4N7HACover16_commandCallbackE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>The command callback that will be called when clicking the cover’s button in the HA panel. </p>
</dd></dl>

</div>
</dd></dl>

</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="ha-camera.html"
       title="previous chapter">← HACamera class</a>
  </li>
  <li class="next">
    <a href="ha-device-tracker.html"
       title="next chapter">HADeviceTracker class →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>