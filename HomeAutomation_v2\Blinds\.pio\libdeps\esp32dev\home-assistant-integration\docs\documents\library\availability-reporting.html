<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>Availability reporting - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="Connection parameters" href="connection-params.html" />
  <link rel="prev" title="Device configuration" href="device-configuration.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Library</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-configuration.html">Device configuration</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="index.html">Library</a> &raquo;</li>
    
    <li>Availability reporting</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="device-configuration.html"
       title="previous chapter">← Device configuration</a>
  </li>
  <li class="next">
    <a href="connection-params.html"
       title="next chapter">Connection parameters →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="availability-reporting">
<h1>Availability reporting<a class="headerlink" href="#availability-reporting" title="Permalink to this headline">¶</a></h1>
<p>Home Assistant allows to track online/offline states of devices and device types.
In this way controls available in the panel will be displayed as disabled if a device is offline.</p>
<p>The library allows to expose state of the entire device (i.e. shared availability) or specific type (sensor, switch, light, etc.).
By default this feature is not enabled to save resources (RAM and flash) but you can easily turn it on as shown below.</p>
<section id="shared-availability">
<h2>Shared availability<a class="headerlink" href="#shared-availability" title="Permalink to this headline">¶</a></h2>
<p>I highly recommend to use shared availability feature as it allows to utilize MQTT LWT.
Basically, shared availability allows to control availability of all types related to a specific device.
For example: if your device has 5 switches and 2 buttons you can control their availability in the HA panel using a single method call.</p>
<p>See example below showing how to enable shared availability of the device.
By default, the device is considered online but you can control its state manually using <code class="docutils literal notranslate"><span class="pre">HADevice::setAvailability(bool</span> <span class="pre">online)</span></code> method.
In most cases you won’t need to control availability manually as the library takes care of availability as long as the device is powered on.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="s">&quot;myUniqueID&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">enableSharedAvailability</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="c1">// device.setAvailability(false); // changes default state to offline</span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>

<span class="w">    </span><span class="c1">// device.setAvailability(true); // you can control availability manually if you want</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="mqtt-lwt">
<h2>MQTT LWT<a class="headerlink" href="#mqtt-lwt" title="Permalink to this headline">¶</a></h2>
<p>The shared availability feature is considered a complete solution only if it’s used with MQTT LWT feature.
Without LWT if the device is powered off then Home Assistant displays it as online.
That’s because availability tracking relies on MQTT messages and if you cut off power of your device then its not capable of publishing the offline message.</p>
<p>When LWT feature is enabled the device becomes offline in the HA panel even if you cut off power supply.
This solution is implemented by MQTT broker that automatically publishes the message when the TCP connection to the device is lost.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="s">&quot;myUniqueID&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">enableSharedAvailability</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">device</span><span class="p">.</span><span class="n">enableLastWill</span><span class="p">();</span><span class="w"></span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="device-type-s-availability">
<h2>Device type’s availability<a class="headerlink" href="#device-type-s-availability" title="Permalink to this headline">¶</a></h2>
<p>There also a way to control availability of specific device types.
Each type can be controlled separately as shown below.
Please note that this solution requires shared availability to be disabled and it’s not supported by LWT.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;Ethernet.h&gt;</span><span class="cp"></span>
<span class="cp">#include</span><span class="w"> </span><span class="cpf">&lt;ArduinoHA.h&gt;</span><span class="cp"></span>

<span class="n">byte</span><span class="w"> </span><span class="n">mac</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mh">0x00</span><span class="p">,</span><span class="w"> </span><span class="mh">0x10</span><span class="p">,</span><span class="w"> </span><span class="mh">0xFA</span><span class="p">,</span><span class="w"> </span><span class="mh">0x6E</span><span class="p">,</span><span class="w"> </span><span class="mh">0x38</span><span class="p">,</span><span class="w"> </span><span class="mh">0x4A</span><span class="p">};</span><span class="w"></span>
<span class="n">EthernetClient</span><span class="w"> </span><span class="n">client</span><span class="p">;</span><span class="w"></span>
<span class="n">HADevice</span><span class="w"> </span><span class="nf">device</span><span class="p">(</span><span class="n">mac</span><span class="p">,</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">mac</span><span class="p">));</span><span class="w"></span>
<span class="n">HAMqtt</span><span class="w"> </span><span class="nf">mqtt</span><span class="p">(</span><span class="n">client</span><span class="p">,</span><span class="w"> </span><span class="n">device</span><span class="p">);</span><span class="w"></span>
<span class="n">HASwitch</span><span class="w"> </span><span class="nf">mySwitch</span><span class="p">(</span><span class="s">&quot;mySwitchId&quot;</span><span class="p">);</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">setup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">Ethernet</span><span class="p">.</span><span class="n">begin</span><span class="p">(</span><span class="n">mac</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// this line enables availability for your switch</span>
<span class="w">    </span><span class="n">mySwitch</span><span class="p">.</span><span class="n">setAvailability</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"> </span><span class="c1">// you can also set it to false</span>

<span class="w">    </span><span class="c1">// ...</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">loop</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// ...</span>

<span class="w">    </span><span class="c1">// you can control availability at runtime as follows:</span>
<span class="w">    </span><span class="n">mySwitch</span><span class="p">.</span><span class="n">setAvailability</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"> </span><span class="c1">// online</span>
<span class="w">    </span><span class="n">mySwitch</span><span class="p">.</span><span class="n">setAvailability</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span><span class="w"> </span><span class="c1">// offline</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="device-configuration.html"
       title="previous chapter">← Device configuration</a>
  </li>
  <li class="next">
    <a href="connection-params.html"
       title="next chapter">Connection parameters →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>