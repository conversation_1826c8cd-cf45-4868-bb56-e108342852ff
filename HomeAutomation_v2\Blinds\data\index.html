<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Blind Control</title>
  <style>
    button {
      font-size: 16px;
      margin: 5px;
    }
  </style>
</head>
<body>

<h2>Blind Control</h2>

<label>Global Step Size:</label>
<input type="number" id="globalStep" value="1000">
<button onclick="updatePositions()">Get Positions</button>

<!-- Motor 1 -->
<h3>Motor 1 - at: <span id="motor0_position">...</span> , Max: <span id="motor0_max">...</span></h3>
<button onclick="controlMotor(1, 'up')">Up</button>
<button onclick="controlMotor(1, 'down')">Down</button>
<button onclick="gotoOpen(1)">Open</button>
<button onclick="gotoClose(1)">Close</button>
<button onclick="setOpenPosition(1)">Set Open Position</button>
<button onclick="setClosePosition(1)">Set Close Position</button>

<!-- Motor 2 -->
<h3>Motor 2 - at: <span id="motor1_position">...</span> , Max: <span id="motor1_max">...</span></h3>
<button onclick="controlMotor(2, 'up')">Up</button>
<button onclick="controlMotor(2, 'down')">Down</button>
<button onclick="gotoOpen(2)">Open</button>
<button onclick="gotoClose(2)">Close</button>
<button onclick="setOpenPosition(2)">Set Open Position</button>
<button onclick="setClosePosition(2)">Set Close Position</button>

<!-- Motor 3 -->
<h3>Motor 3 - at: <span id="motor2_position">...</span> , Max: <span id="motor2_max">...</span></h3>
<button onclick="controlMotor(3, 'up')">Up</button>
<button onclick="controlMotor(3, 'down')">Down</button>
<button onclick="gotoOpen(3)">Open</button>
<button onclick="gotoClose(3)">Close</button>
<button onclick="setOpenPosition(3)">Set Open Position</button>
<button onclick="setClosePosition(3)">Set Close Position</button>

<!-- Motor 4 -->
<h3>Motor 4 - at: <span id="motor3_position">...</span> , Max: <span id="motor3_max">...</span></h3>
<button onclick="controlMotor(4, 'up')">Up</button>
<button onclick="controlMotor(4, 'down')">Down</button>
<button onclick="gotoOpen(4)">Open</button>
<button onclick="gotoClose(4)">Close</button>
<button onclick="setOpenPosition(4)">Set Open Position</button>
<button onclick="setClosePosition(4)">Set Close Position</button>

<script>
  function setGlobalStep() {
    var step = document.getElementById("globalStep").value;
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("action=setGlobalStep&step=" + step);
  }

  function getMotorPositions() {
    var xhr = new XMLHttpRequest();

    xhr.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
       // Typical action to be performed when the document is ready:
       var myArr = JSON.parse(this.responseText);
       for(var i = 0; i < 4; i++) {
          document.getElementById("motor" + i + "_position").innerHTML = myArr[i];
       }
      }
    };

    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("action=getMotorPosition&motor=0");
  }

  function getMotorMax() {
    var xhr = new XMLHttpRequest();

    xhr.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
       // Typical action to be performed when the document is ready:
       var myArr = JSON.parse(this.responseText);
       for(var i = 0; i < 4; i++) {
          document.getElementById("motor" + i + "_max").innerHTML = myArr[i];
       }
      }
    };

    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("action=getMotorMax&motor=0");
  }

  function gotoOpen(motor) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("motor=" + motor + "&action=gotoOpen");
  }

  function gotoClose(motor) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("motor=" + motor + "&action=gotoClose");
  }

  function controlMotor(motor, direction) {
    var step = document.getElementById("globalStep").value;
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("motor=" + motor + "&action=control&direction=" + direction + "&step=" + step);
  }

  function setOpenPosition(motor) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("motor=" + motor + "&action=setOpen");
  }

  function setClosePosition(motor) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/motor", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("motor=" + motor + "&action=setClose");
  }


  function updatePositions() {
    getMotorPositions();
    getMotorMax();
  }

  window.onload = function () {
    updatePositions();
  }


</script>

</body>
</html>
