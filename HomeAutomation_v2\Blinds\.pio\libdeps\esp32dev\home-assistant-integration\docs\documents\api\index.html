<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>API reference - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="Core API" href="core/index.html" />
  <link rel="prev" title="Compiler macros" href="../library/compiler-macros.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
    <li>API reference</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="../library/compiler-macros.html"
       title="previous chapter">← Compiler macros</a>
  </li>
  <li class="next">
    <a href="core/index.html"
       title="next chapter">Core API →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="api-reference">
<h1>API reference<a class="headerlink" href="#api-reference" title="Permalink to this headline">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="core/index.html">Core API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="core/ha-device.html">HADevice class</a></li>
<li class="toctree-l2"><a class="reference internal" href="core/ha-mqtt.html">HAMqtt class</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="device-types/index.html">Device types API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-base-device-type.html">HABaseDeviceType class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-binary-sensor.html">HABinarySensor class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-button.html">HAButton class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-camera.html">HACamera class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-cover.html">HACover class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-device-tracker.html">HADeviceTracker class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-device-trigger.html">HADeviceTrigger class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-fan.html">HAFan class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-hvac.html">HAHVAC class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-light.html">HALight class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-lock.html">HALock class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-number.html">HANumber class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-scene.html">HAScene class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-select.html">HASelect class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-sensor.html">HASensor class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-sensor-number.html">HASensorNumber class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-switch.html">HASwitch class</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types/ha-tag-scanner.html">HATagScanner class</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="utils/index.html">Utils API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="utils/ha-numeric.html">HANumeric class</a></li>
<li class="toctree-l2"><a class="reference internal" href="utils/ha-serializer.html">HASerializer class</a></li>
<li class="toctree-l2"><a class="reference internal" href="utils/ha-serializer-array.html">HASerializerArray class</a></li>
<li class="toctree-l2"><a class="reference internal" href="utils/ha-utils.html">HAUtils class</a></li>
</ul>
</li>
</ul>
</div>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="../library/compiler-macros.html"
       title="previous chapter">← Compiler macros</a>
  </li>
  <li class="next">
    <a href="core/index.html"
       title="next chapter">Core API →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>