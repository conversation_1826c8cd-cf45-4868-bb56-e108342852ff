#include <Arduino.h>
#include <EEPROM.h>
#include <WiFi.h>
#include "ESPAsyncWebServer.h"
#include <AsyncElegantOTA.h>
#include <ArduinoHA.h>
#include <MotorController.h>
#include <RCSwitch.h>
#include <SPIFFS.h>

#define BROKER_ADDR IPAddress(192,168,1,223)
#define BROKER_USERNAME     "homeassistant" // replace with your credentials
#define BROKER_PASSWORD     "ohfohPheocaToiN9ah<PERSON>iphahphooZoas5iengua9thachie1Zoh4dooSieshi6"

#define WIFI_NETWORK "Alex"
#define WIFI_PASSWORD "!remix1401@"
#define WIFI_TIMEOUT_MS 20000 // 20 second WiFi connection timeout
#define WIFI_RECOVER_TIME_MS 30000 // Wait 30 seconds after a failed connection attempt


#define RF_ENABLE 15
#define RF_DATA 16

#define MOTOR_1_EN 21
#define MOTOR_1_DIR 23
#define MOTOR_1_STEP 22
#define MOTOR_1_ENDSTOP 26
#define MOTOR_1_RF_BUTTON 6106657

#define MOTOR_2_EN 19
#define MOTOR_2_DIR 5
#define MOTOR_2_STEP 18
#define MOTOR_2_ENDSTOP 25
#define MOTOR_2_RF_BUTTON 6106658

#define MOTOR_3_EN 17
#define MOTOR_3_DIR 0
#define MOTOR_3_STEP 13
#define MOTOR_3_ENDSTOP 33
#define MOTOR_3_RF_BUTTON 6106660

#define MOTOR_4_EN 27
#define MOTOR_4_DIR 14
#define MOTOR_4_STEP 12
#define MOTOR_4_ENDSTOP 32
#define MOTOR_4_RF_BUTTON 6106664

MotorController motors[4] = {
  MotorController(0, MOTOR_1_STEP, MOTOR_1_DIR, MOTOR_1_EN),
  MotorController(1, MOTOR_2_STEP, MOTOR_2_DIR, MOTOR_2_EN),
  MotorController(2, MOTOR_3_STEP, MOTOR_3_DIR, MOTOR_3_EN),
  MotorController(3, MOTOR_4_STEP, MOTOR_4_DIR, MOTOR_4_EN)
};


RCSwitch myRemote = RCSwitch();
int lastButtonPressed[4] = {0,0,0,0};


AsyncWebServer server(80);

WiFiClient client;
HADevice device;
HAMqtt mqtt(client, device, 10);

HACover  cover1("cover1", HACover::Features::PositionFeature);
HACover  cover2("cover2", HACover::Features::PositionFeature);
HACover  cover3("cover3", HACover::Features::PositionFeature);
HACover  cover4("cover4", HACover::Features::PositionFeature);


HACover* haCovers[] = {&cover1, &cover2, &cover3, &cover4};


char webResponse[1024];
char debugString[128];

void onCoverCommand(HACover::CoverCommand cmd, HACover* sender) {

  int motor_id = 0;
  for(int i = 0; i < 4; i++) {
    if(sender == haCovers[i]) {
      motor_id = i;
      break;
    }
  }

  if (cmd == HACover::CommandOpen) {
    sender->setState(HACover::StateOpening, true); // report state back to the HA
    motors[motor_id].open();
  } else if (cmd == HACover::CommandClose) {
    sender->setState(HACover::StateClosing, true); // report state back to the HA
    motors[motor_id].close();
  } else if (cmd == HACover::CommandStop) {
    sender->setState(HACover::StateStopped, true); // report state back to the HA
    motors[motor_id].stop();
  }

  sender->setPosition(motors[motor_id].getCurrentPositionNormalized(), true);
  
  // Available states:
  // HACover::StateClosed
  // HACover::StateClosing
  // HACover::StateOpen
  // HACover::StateOpening
  // HACover::StateStopped
}



void keepWiFiAlive(void * parameter){
    for(;;){
        if(WiFi.status() == WL_CONNECTED){
            vTaskDelay(10000 / portTICK_PERIOD_MS);
            continue;
        }

        Serial.println("[WIFI] Connecting");
        WiFi.mode(WIFI_STA);
        WiFi.begin(WIFI_NETWORK, WIFI_PASSWORD);

        unsigned long startAttemptTime = millis();

        // Keep looping while we're not connected and haven't reached the timeout
        while (WiFi.status() != WL_CONNECTED && 
                millis() - startAttemptTime < WIFI_TIMEOUT_MS){}

        // When we couldn't make a WiFi connection (or the timeout expired)
		  // sleep for a while and then retry.
        if(WiFi.status() != WL_CONNECTED){
            Serial.println("[WIFI] FAILED");
            vTaskDelay(WIFI_RECOVER_TIME_MS / portTICK_PERIOD_MS);
			  continue;
        }

        Serial.println("[WIFI] Connected: " + WiFi.localIP());
    }
}



void setup() {
  pinMode(RF_ENABLE, OUTPUT);
  pinMode(RF_DATA, INPUT);

  pinMode(MOTOR_1_ENDSTOP, INPUT_PULLUP);
  pinMode(MOTOR_2_ENDSTOP, INPUT_PULLUP);
  pinMode(MOTOR_3_ENDSTOP, INPUT_PULLUP);
  pinMode(MOTOR_4_ENDSTOP, INPUT_PULLUP);

  
  Serial.begin(115200);
  delay(500);


  if(!SPIFFS.begin(true)){
    Serial.println("An Error has occurred while mounting SPIFFS");
    return;
  }
  Serial.println("SPIFFS mounted successfully");

  Serial.println();
  Serial.print("ESP Blinds Board MAC Address: ");
  Serial.println(WiFi.macAddress());

  byte mac[6];
  WiFi.macAddress(mac);
  device.setUniqueId(mac, sizeof(mac));
  device.setManufacturer("Espressif");
  device.setModel("ESP32 Wroom-32D");
  device.setName("Blinds controller");
  device.setSoftwareVersion("0.0.1");

  //WiFi.printDiag(Serial);
  WiFi.mode(WIFI_STA);
  WiFi.setHostname("ESP_Blinds");
  WiFi.begin(WIFI_NETWORK, WIFI_PASSWORD);
  WiFi.setSleep(WIFI_PS_NONE); 
  
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println(WiFi.status());
    //Something went wrong
    if(WiFi.status() == WL_CONNECTION_LOST || WiFi.status() == WL_CONNECT_FAILED || millis() > 5000) {
      WiFi.reconnect();
    }
  }

  WiFi.setAutoConnect(true);
  WiFi.setAutoReconnect(true);
  
  Serial.println(WiFi.localIP());
  
  //server.on("/", HTTP_GET, [](AsyncWebServerRequest *request){
//    sprintf(webResponse,"OK Blinds\nWiFi RRSI: %d", WiFi.RSSI());
    //request->send(200, "text/plain", webResponse);
  //});

  // Define web server routes
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request) {
    request->send(SPIFFS, "/index.html", "text/html");
  });

  server.on("/resetMe", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "text/plain", "Restart in 3 seconds");
    delay(3000);
    ESP.restart();
  });


  // Define AJAX endpoints
  server.on("/motor", HTTP_POST, [](AsyncWebServerRequest *request) {
    int motor_id = request->arg("motor").toInt() - 1;
    String action = request->arg("action");
    
    if (action == "setOpen") {
      // Handle setting open position
      motors[motor_id].setCurrentPosition(0);
      Serial.print("Motor ");
      Serial.print(motor_id);
      Serial.println(" current position set to 0");
    } else if (action == "setClose") {
      // Handle setting close position
      motors[motor_id].setMaxPosition(motors[motor_id].getCurrentPosition());
      Serial.print("Motor ");
      Serial.print(motor_id);
      Serial.print(" max position set to ");
      Serial.println(motors[motor_id].getCurrentPosition());
    } else if(action == "control") {
      String direction = request->arg("direction");
      int step = request->arg("step").toInt();

      Serial.print("Motor ");
      Serial.print(motor_id);
      
      if(direction == "up") {
        if((motors[motor_id].getCurrentPosition() - step) < 0) {
          motors[motor_id].setCurrentPosition(step);
        }
        motors[motor_id].goToPosition(motors[motor_id].getCurrentPosition() - step);

        Serial.print(" go to  ");
        Serial.println(motors[motor_id].getCurrentPosition() - step);

      } else {
        if((motors[motor_id].getCurrentPosition() + step) > motors[motor_id].getMaxPosition()) {
          motors[motor_id].setMaxPosition(motors[motor_id].getMaxPosition() + step);
        }
        motors[motor_id].goToPosition(motors[motor_id].getCurrentPosition() + step);
        Serial.print(" go to  ");
        Serial.println(motors[motor_id].getCurrentPosition() + step);
      }
    } else if(action == "getMotorPosition") {
      String response = "[";
      response += motors[0].getCurrentPosition();
      response += ",";
      response += motors[1].getCurrentPosition();
      response += ",";
      response += motors[2].getCurrentPosition();
      response += ",";
      response += motors[3].getCurrentPosition();
      response += "]";
      request->send(200, "text/plain", response);
      return;
    } else if(action == "getMotorMax") {
      String response = "[";
      response += motors[0].getMaxPosition();
      response += ",";
      response += motors[1].getMaxPosition();
      response += ",";
      response += motors[2].getMaxPosition();
      response += ",";
      response += motors[3].getMaxPosition();
      response += "]";
      request->send(200, "text/plain", response);
      return;
    } else if(action == "getNormalizedPosition") {
      String response = "[";
      response += motors[0].getCurrentPositionNormalized();
      response += ",";
      response += motors[1].getCurrentPositionNormalized();
      response += ",";
      response += motors[2].getCurrentPositionNormalized();
      response += ",";
      response += motors[3].getCurrentPositionNormalized();
      response += "]";
      request->send(200, "text/plain", response);
      return;
    } else if(action == "gotoOpen") {
      motors[motor_id].open();
    } else if(action == "gotoClose") {
      motors[motor_id].close();
    }

    request->send(200, "text/plain", "OK");
  });
  server.serveStatic("/", SPIFFS, "/").setDefaultFile("index.htm");
  
  AsyncElegantOTA.begin(&server);

  server.begin();

  cover1.onCommand(onCoverCommand);
  cover1.setName("Cover 1");
  
  cover2.onCommand(onCoverCommand);
  cover2.setName("Cover 2");
  
  cover3.onCommand(onCoverCommand);
  cover3.setName("Cover 3");
  
  cover4.onCommand(onCoverCommand);
  cover4.setName("Cover 4");
  
  
  //uptimeSensor.setName("Kitchen Fan Uptime");
  //uptimeSensor.setIcon("mdi:timer-refresh-outline");
  //uptimeSensor.setUnitOfMeasurement("s");

  mqtt.begin(BROKER_ADDR, BROKER_USERNAME, BROKER_PASSWORD);

  //Enable the receiver
  digitalWrite(RF_ENABLE, LOW);
  myRemote.enableReceive(digitalPinToInterrupt(RF_DATA));
  
  motors[0].begin();
  motors[1].begin();
  motors[2].begin();
  motors[3].begin();


  xTaskCreatePinnedToCore(
    keepWiFiAlive,
    "keepWiFiAlive",  // Task name
    5000,             // Stack size (bytes)
    NULL,             // Parameter
    1,                // Task priority
    NULL,             // Task handle
    ARDUINO_RUNNING_CORE
  );

}



void processSerialCommand() {
  String command = Serial.readStringUntil('\n');
  command.trim();

  if (command.startsWith("SETPOS")) {
    // Format: SETPOS <position>
    int newPos = command.substring(7).toInt();
    motors[0].setCurrentPosition(newPos);
    Serial.println("Current position set to: " + String(newPos));
  } else if (command.startsWith("MOVE")) {
    // Format: MOVE <steps>
    int steps = command.substring(5).toInt();
    motors[0].goToPosition(motors[0].getCurrentPosition() + steps);
    Serial.println("Moving to position: " + String(motors[0].getCurrentPosition() + steps));
  } else if (command.startsWith("GOTO")) {
    // Format: GOTO <position>
    int gotoPos = command.substring(5).toInt();
    motors[0].goToPosition(gotoPos);
    Serial.println("Moving to position: " + String(gotoPos));
  } else if (command.startsWith("SETMAX")) {
    // Format: SETMAX <maxPosition>
    int maxPos = command.substring(7).toInt();
    motors[0].setMaxPosition(maxPos);
    Serial.println("Max position set to: " + String(maxPos));
  } else if (command == "GETPOS") {
    // Format: GETPOS
    int currentPos = motors[0].getCurrentPosition();
    Serial.println("Current Position: " + String(currentPos));
  } else {
    Serial.println("Invalid command.");
  }
}

void handleRemoteSignal() {
  unsigned long buttonCode = myRemote.getReceivedValue();
  int motorId;

  switch (buttonCode)
  {
    case MOTOR_1_RF_BUTTON:
      motorId = 0;
      break;
    case MOTOR_2_RF_BUTTON:
      motorId = 1;
      break;
    case MOTOR_3_RF_BUTTON:
      motorId = 2;
      break;
    case MOTOR_4_RF_BUTTON:
      motorId = 3;
      break;
    default:
      return;
  }
  
  if(motors[motorId].getIsEnabled()) {
    //Already going somewere
    return;
  }

  if(motors[motorId].getCurrentPosition() == 0) {
    motors[motorId].goToPosition(motors[motorId].getMaxPosition());
  } else {
    motors[motorId].goToPosition(0);
  }
}

void updateHaStates() {
  for(int id = 0; id < 4; id++) {
    //If the motor is runing -> Do nothing
    if(motors[id].getIsEnabled() || !motors[id].getHaveUpdate()) {
      continue;
    }

    int currentMotorPosition = motors[id].getCurrentPositionNormalized();
    haCovers[id]->setPosition(currentMotorPosition);

    if(currentMotorPosition == 0) {
      haCovers[id]->setState(HACover::StateOpen);
    } else if(currentMotorPosition >= 100) {
      haCovers[id]->setState(HACover::StateClosed);
    } else {
      haCovers[id]->setState(HACover::StateStopped);
    }

    
    motors[id].clearHaveUpdate();
  }
}

void loop() {

  mqtt.loop();

  if (Serial.available() > 0) {
    processSerialCommand();
  }


  if(myRemote.available()) {
    handleRemoteSignal();
    myRemote.resetAvailable();
  }

  //updateHaStates();



  // Non-blocking update for all motors
  for (int i = 0; i < 4; ++i) {
    motors[i].update();
  }

}