<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>HADeviceTrigger class - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../../_static/theme-vendors.js"></script> -->
      <script src="../../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../../genindex.html" />
  <link rel="search" title="Search" href="../../../search.html" />
  <link rel="next" title="HAFan class" href="ha-fan.html" />
  <link rel="prev" title="HADeviceTracker class" href="ha-device-tracker.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">API reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../core/index.html">Core API</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="../index.html">API reference</a> &raquo;</li>
    
      <li><a href="index.html">Device types API</a> &raquo;</li>
    
    <li>HADeviceTrigger class</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="ha-device-tracker.html"
       title="previous chapter">← HADeviceTracker class</a>
  </li>
  <li class="next">
    <a href="ha-fan.html"
       title="next chapter">HAFan class →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="hadevicetrigger-class">
<h1>HADeviceTrigger class<a class="headerlink" href="#hadevicetrigger-class" title="Permalink to this headline">¶</a></h1>
<dl class="cpp class">
<dt class="sig sig-object cpp" id="_CPPv415HADeviceTrigger">
<span id="_CPPv315HADeviceTrigger"></span><span id="_CPPv215HADeviceTrigger"></span><span id="HADeviceTrigger"></span><span class="target" id="class_h_a_device_trigger"></span><span class="k"><span class="pre">class</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTrigger</span></span></span><span class="w"> </span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="k"><span class="pre">public</span></span><span class="w"> </span><a class="reference internal" href="ha-base-device-type.html#_CPPv416HABaseDeviceType" title="HABaseDeviceType"><span class="n"><span class="pre">HABaseDeviceType</span></span></a><a class="headerlink" href="#_CPPv415HADeviceTrigger" title="Permalink to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="#class_h_a_device_trigger"><span class="std std-ref">HADeviceTrigger</span></a> allows to a custom trigger that can be used in the Home Assistant automation. For example, it can be a wall switch that produces <code class="docutils literal notranslate"><span class="pre">press</span></code> and <code class="docutils literal notranslate"><span class="pre">long_press</span></code> actions.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You can find more information about this entity in the Home Assistant documentation: <a class="reference external" href="https://www.home-assistant.io/integrations/device_trigger.mqtt/">https://www.home-assistant.io/integrations/device_trigger.mqtt/</a> </p>
</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-types">Public Types</p>
<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1d"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">TriggerType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Built-in types of the trigger. </p>
<p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType20ButtonShortPressTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType20ButtonShortPressTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType20ButtonShortPressTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1dad6717b65d447ef8d5cbb27033c8faafe"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonShortPressType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType20ButtonShortPressTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType22ButtonShortReleaseTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType22ButtonShortReleaseTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType22ButtonShortReleaseTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1da97cab92fc1a4c5d117596de62f44eea5"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonShortReleaseType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType22ButtonShortReleaseTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType19ButtonLongPressTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType19ButtonLongPressTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType19ButtonLongPressTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1dac0a68af076e090149de9e4a919309396"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonLongPressType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType19ButtonLongPressTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType21ButtonLongReleaseTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType21ButtonLongReleaseTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType21ButtonLongReleaseTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1dab8db1cbd9ee8cfd2101e3c756898ff5c"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonLongReleaseType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType21ButtonLongReleaseTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType21ButtonDoublePressTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType21ButtonDoublePressTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType21ButtonDoublePressTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1dae265d114060e83a3a4db482ff40a842e"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonDoublePressType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType21ButtonDoublePressTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType21ButtonTriplePressTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType21ButtonTriplePressTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType21ButtonTriplePressTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1da5847c8c8416254feb5253c6ce6fafcfd"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonTriplePressType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType21ButtonTriplePressTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType24ButtonQuadruplePressTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType24ButtonQuadruplePressTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType24ButtonQuadruplePressTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1da53f99bf193a8b55271b566156747b818"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonQuadruplePressType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType24ButtonQuadruplePressTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger11TriggerType24ButtonQuintuplePressTypeE">
<span id="_CPPv3N15HADeviceTrigger11TriggerType24ButtonQuintuplePressTypeE"></span><span id="_CPPv2N15HADeviceTrigger11TriggerType24ButtonQuintuplePressTypeE"></span><span class="target" id="class_h_a_device_trigger_1afa5293814079971c7a3c1d87246d0e1daf66827148f676fdebf1c4e643f93ba10"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ButtonQuintuplePressType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger11TriggerType24ButtonQuintuplePressTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

<dl class="cpp enum">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2"></span><span class="k"><span class="pre">enum</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">TriggerSubtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Built-in subtypes of the trigger. </p>
<p><em>Values:</em></p>
<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype13TurnOnSubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype13TurnOnSubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype13TurnOnSubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2a4d5ebff1c4268ac380e55e1fd1736bde"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">TurnOnSubtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype13TurnOnSubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14TurnOffSubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14TurnOffSubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14TurnOffSubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2aed8699d3de9d8f9539142c41cad62d89"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">TurnOffSubtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14TurnOffSubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14Button1SubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14Button1SubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14Button1SubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2aa5aebea4d4bbfef72e4830c5502596ac"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Button1Subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button1SubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14Button2SubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14Button2SubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14Button2SubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2a2d6fdf69024c6f926db0e05180eba712"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Button2Subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button2SubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14Button3SubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14Button3SubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14Button3SubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2ac4623bbd8a5d43c0d2ecd9dd93d88ce8"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Button3Subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button3SubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14Button4SubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14Button4SubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14Button4SubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2ac7a49891229d57fc3057f910a542205f"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Button4Subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button4SubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14Button5SubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14Button5SubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14Button5SubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2a8eb212a635673f429223bdd11d93ea4a"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Button5Subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button5SubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp enumerator">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14TriggerSubtype14Button6SubtypeE">
<span id="_CPPv3N15HADeviceTrigger14TriggerSubtype14Button6SubtypeE"></span><span id="_CPPv2N15HADeviceTrigger14TriggerSubtype14Button6SubtypeE"></span><span class="target" id="class_h_a_device_trigger_1aed4ca461bb696c41399027c0d649aaa2acd5c5a533845879683dfb13a5d3cee19"></span><span class="k"><span class="pre">enumerator</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Button6Subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14TriggerSubtype14Button6SubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-public-functions">Public Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger15HADeviceTriggerEPKcPKc">
<span id="_CPPv3N15HADeviceTrigger15HADeviceTriggerEPKcPKc"></span><span id="_CPPv2N15HADeviceTrigger15HADeviceTriggerEPKcPKc"></span><span id="HADeviceTrigger::HADeviceTrigger__cCP.cCP"></span><span class="target" id="class_h_a_device_trigger_1a381ad9d7051b424050c9985c5fccf996"></span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTrigger</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">type</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">subtype</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger15HADeviceTriggerEPKcPKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Creates the device trigger with a custom type and subtype. For example, it can be <code class="docutils literal notranslate"><span class="pre">click</span></code> as the type and <code class="docutils literal notranslate"><span class="pre">btn0</span></code> as the subtype. Please note that combination of the type and subtype needs to be unique in a scope of your device.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>type</strong> – String representation of the type. </p></li>
<li><p><strong>subtype</strong> – String representation of the subtype. </p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger15HADeviceTriggerE11TriggerTypePKc">
<span id="_CPPv3N15HADeviceTrigger15HADeviceTriggerE11TriggerTypePKc"></span><span id="_CPPv2N15HADeviceTrigger15HADeviceTriggerE11TriggerTypePKc"></span><span id="HADeviceTrigger::HADeviceTrigger__TriggerType.cCP"></span><span class="target" id="class_h_a_device_trigger_1a87671ef60055147a9f592863dec26352"></span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTrigger</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#_CPPv4N15HADeviceTrigger11TriggerTypeE" title="HADeviceTrigger::TriggerType"><span class="n"><span class="pre">TriggerType</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">type</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">subtype</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger15HADeviceTriggerE11TriggerTypePKc" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Creates the device trigger with a built-in type and a custom subtype. For example, it can be <code class="docutils literal notranslate"><span class="pre">HADeviceTrigger::ButtonShortPressType</span></code> as the type and <code class="docutils literal notranslate"><span class="pre">btn0</span></code> as the subtype. Please note that combination of the type and subtype needs to be unique in a scope of your device.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>type</strong> – Built-in type of the trigger. </p></li>
<li><p><strong>subtype</strong> – String representation of the subtype. </p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger15HADeviceTriggerEPKc14TriggerSubtype">
<span id="_CPPv3N15HADeviceTrigger15HADeviceTriggerEPKc14TriggerSubtype"></span><span id="_CPPv2N15HADeviceTrigger15HADeviceTriggerEPKc14TriggerSubtype"></span><span id="HADeviceTrigger::HADeviceTrigger__cCP.TriggerSubtype"></span><span class="target" id="class_h_a_device_trigger_1a8a466d3acdea037af5ab9dd304b78a84"></span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTrigger</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n sig-param"><span class="pre">type</span></span>, <a class="reference internal" href="#_CPPv4N15HADeviceTrigger14TriggerSubtypeE" title="HADeviceTrigger::TriggerSubtype"><span class="n"><span class="pre">TriggerSubtype</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">subtype</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger15HADeviceTriggerEPKc14TriggerSubtype" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Creates the device trigger with a custom type and a built-in subtype. For example, it can be <code class="docutils literal notranslate"><span class="pre">click</span></code> as the type and <code class="docutils literal notranslate"><span class="pre">HADeviceTrigger::Button1Subtype</span></code> as the subtype. Please note that combination of the type and subtype needs to be unique in a scope of your device.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>type</strong> – String representation of the subtype. </p></li>
<li><p><strong>subtype</strong> – Built-in subtype of the trigger. </p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger15HADeviceTriggerE11TriggerType14TriggerSubtype">
<span id="_CPPv3N15HADeviceTrigger15HADeviceTriggerE11TriggerType14TriggerSubtype"></span><span id="_CPPv2N15HADeviceTrigger15HADeviceTriggerE11TriggerType14TriggerSubtype"></span><span id="HADeviceTrigger::HADeviceTrigger__TriggerType.TriggerSubtype"></span><span class="target" id="class_h_a_device_trigger_1a905404693ada525dd1d7bda17dbd4706"></span><span class="sig-name descname"><span class="n"><span class="pre">HADeviceTrigger</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#_CPPv4N15HADeviceTrigger11TriggerTypeE" title="HADeviceTrigger::TriggerType"><span class="n"><span class="pre">TriggerType</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">type</span></span>, <a class="reference internal" href="#_CPPv4N15HADeviceTrigger14TriggerSubtypeE" title="HADeviceTrigger::TriggerSubtype"><span class="n"><span class="pre">TriggerSubtype</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">subtype</span></span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger15HADeviceTriggerE11TriggerType14TriggerSubtype" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Creates the device trigger with a built-in type and built-in subtype. For example, it can be <code class="docutils literal notranslate"><span class="pre">HADeviceTrigger::ButtonShortPressType</span></code> as the type and <code class="docutils literal notranslate"><span class="pre">HADeviceTrigger::Button1Subtype</span></code> as the subtype. Please note that combination of the type and subtype needs to be unique in a scope of your device.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>type</strong> – Built-in type of the trigger. </p></li>
<li><p><strong>subtype</strong> – Built-in subtype of the trigger. </p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTriggerD0Ev">
<span id="_CPPv3N15HADeviceTriggerD0Ev"></span><span id="_CPPv2N15HADeviceTriggerD0Ev"></span><span id="HADeviceTrigger::~HADeviceTrigger"></span><span class="target" id="class_h_a_device_trigger_1a5c38d55de3608c0cea7e247d96a281b4"></span><span class="sig-name descname"><span class="n"><span class="pre">~HADeviceTrigger</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTriggerD0Ev" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Frees memory allocated by the class. </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger7triggerEv">
<span id="_CPPv3N15HADeviceTrigger7triggerEv"></span><span id="_CPPv2N15HADeviceTrigger7triggerEv"></span><span id="HADeviceTrigger::trigger"></span><span class="target" id="class_h_a_device_trigger_1af5ccc2a8c7725822d5fff6be89432903"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">trigger</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger7triggerEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Publishes MQTT message with the trigger event. The published message is not retained.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns</dt>
<dd class="field-odd"><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if MQTT message has been published successfully. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger7getTypeEv">
<span id="_CPPv3NK15HADeviceTrigger7getTypeEv"></span><span id="_CPPv2NK15HADeviceTrigger7getTypeEv"></span><span id="HADeviceTrigger::getTypeC"></span><span class="target" id="class_h_a_device_trigger_1a41c0dfc7efb9afbeebda68200f1ab368"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getType</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger7getTypeEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns the type of the trigger. If the built-in type is used the returned value points to the flash memory. Use <code class="docutils literal notranslate"><a class="reference internal" href="#class_h_a_device_trigger_1a9c71bfeb19fca803eb2f1c9e2f0e9320"><span class="std std-ref"><span class="pre">HADeviceTrigger::isProgmemType</span></span></a></code> to verify if the returned value is the progmem pointer.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns</dt>
<dd class="field-odd"><p>Pointer to the type. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger13isProgmemTypeEv">
<span id="_CPPv3NK15HADeviceTrigger13isProgmemTypeEv"></span><span id="_CPPv2NK15HADeviceTrigger13isProgmemTypeEv"></span><span id="HADeviceTrigger::isProgmemTypeC"></span><span class="target" id="class_h_a_device_trigger_1a9c71bfeb19fca803eb2f1c9e2f0e9320"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">isProgmemType</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger13isProgmemTypeEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the built-in type was assigned to the trigger. </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger10getSubtypeEv">
<span id="_CPPv3NK15HADeviceTrigger10getSubtypeEv"></span><span id="_CPPv2NK15HADeviceTrigger10getSubtypeEv"></span><span id="HADeviceTrigger::getSubtypeC"></span><span class="target" id="class_h_a_device_trigger_1ac7e88ab8a8c25414e7b424ade446f1bd"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getSubtype</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger10getSubtypeEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns the subtype of the trigger. If the built-in subtype is used the returned value points to the flash memory. Use <code class="docutils literal notranslate"><a class="reference internal" href="#class_h_a_device_trigger_1a590f7b3442517232be40ea28753b5439"><span class="std std-ref"><span class="pre">HADeviceTrigger::isProgmemSubtype</span></span></a></code> to verify if the returned value is the progmem pointer.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns</dt>
<dd class="field-odd"><p>Pointer to the subtype. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger16isProgmemSubtypeEv">
<span id="_CPPv3NK15HADeviceTrigger16isProgmemSubtypeEv"></span><span id="_CPPv2NK15HADeviceTrigger16isProgmemSubtypeEv"></span><span id="HADeviceTrigger::isProgmemSubtypeC"></span><span class="target" id="class_h_a_device_trigger_1a590f7b3442517232be40ea28753b5439"></span><span class="k"><span class="pre">inline</span></span><span class="w"> </span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">isProgmemSubtype</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger16isProgmemSubtypeEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">true</span></code> if the built-in subtype was assigned to the trigger. </p>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-protected-functions">Protected Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger15buildSerializerEv">
<span id="_CPPv3N15HADeviceTrigger15buildSerializerEv"></span><span id="_CPPv2N15HADeviceTrigger15buildSerializerEv"></span><span id="HADeviceTrigger::buildSerializer"></span><span class="target" id="class_h_a_device_trigger_1a84d6b66acdd287caa6c5b32bdfa07322"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buildSerializer</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger15buildSerializerEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger15onMqttConnectedEv">
<span id="_CPPv3N15HADeviceTrigger15onMqttConnectedEv"></span><span id="_CPPv2N15HADeviceTrigger15onMqttConnectedEv"></span><span id="HADeviceTrigger::onMqttConnected"></span><span class="target" id="class_h_a_device_trigger_1a27f47a60fcad7d095f2349bac4317365"></span><span class="k"><span class="pre">virtual</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">onMqttConnected</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">override</span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger15onMqttConnectedEv" title="Permalink to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-functions">Private Functions</p>
<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger15calculateIdSizeEv">
<span id="_CPPv3NK15HADeviceTrigger15calculateIdSizeEv"></span><span id="_CPPv2NK15HADeviceTrigger15calculateIdSizeEv"></span><span id="HADeviceTrigger::calculateIdSizeC"></span><span class="target" id="class_h_a_device_trigger_1a703d68ef618ce49413f4659e2fbf3f99"></span><span class="n"><span class="pre">uint16_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">calculateIdSize</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger15calculateIdSizeEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Calculates desired size of the unique ID based on the type and subtype that were passed to the constructor. </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger13buildUniqueIdEv">
<span id="_CPPv3N15HADeviceTrigger13buildUniqueIdEv"></span><span id="_CPPv2N15HADeviceTrigger13buildUniqueIdEv"></span><span id="HADeviceTrigger::buildUniqueId"></span><span class="target" id="class_h_a_device_trigger_1ae532e1ff2e712f20259e0a32a6d4a378"></span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buildUniqueId</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger13buildUniqueIdEv" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Builds the unique ID of the device’s type based on the type and subtype that were passed to the constructor. </p>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger20determineProgmemTypeE11TriggerType">
<span id="_CPPv3NK15HADeviceTrigger20determineProgmemTypeE11TriggerType"></span><span id="_CPPv2NK15HADeviceTrigger20determineProgmemTypeE11TriggerType"></span><span id="HADeviceTrigger::determineProgmemType__TriggerTypeC"></span><span class="target" id="class_h_a_device_trigger_1aa58fe524568ee959afa04136b297d752"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">determineProgmemType</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#_CPPv4N15HADeviceTrigger11TriggerTypeE" title="HADeviceTrigger::TriggerType"><span class="n"><span class="pre">TriggerType</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">type</span></span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger20determineProgmemTypeE11TriggerType" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns pointer to the flash memory that represents the given type.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>subtype</strong> – Built-in type enum’s value. </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Pointer to the flash memory if the given type is supported. For the unsupported type the nullptr is returned. </p>
</dd>
</dl>
</dd></dl>

<dl class="cpp function">
<dt class="sig sig-object cpp" id="_CPPv4NK15HADeviceTrigger23determineProgmemSubtypeE14TriggerSubtype">
<span id="_CPPv3NK15HADeviceTrigger23determineProgmemSubtypeE14TriggerSubtype"></span><span id="_CPPv2NK15HADeviceTrigger23determineProgmemSubtypeE14TriggerSubtype"></span><span id="HADeviceTrigger::determineProgmemSubtype__TriggerSubtypeC"></span><span class="target" id="class_h_a_device_trigger_1a86973d5f7680f0b2c81746b603b9607a"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">determineProgmemSubtype</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#_CPPv4N15HADeviceTrigger14TriggerSubtypeE" title="HADeviceTrigger::TriggerSubtype"><span class="n"><span class="pre">TriggerSubtype</span></span></a><span class="w"> </span><span class="n sig-param"><span class="pre">subtype</span></span><span class="sig-paren">)</span><span class="w"> </span><span class="k"><span class="pre">const</span></span><a class="headerlink" href="#_CPPv4NK15HADeviceTrigger23determineProgmemSubtypeE14TriggerSubtype" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Returns pointer to the flash memory that represents the given subtype.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>subtype</strong> – Built-in subtype enum’s value. </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p>Pointer to the flash memory if the given subtype is supported. For the unsupported subtype the nullptr is returned. </p>
</dd>
</dl>
</dd></dl>

</div>
<div class="breathe-sectiondef docutils container">
<p class="breathe-sectiondef-title rubric" id="breathe-section-title-private-members">Private Members</p>
<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger5_typeE">
<span id="_CPPv3N15HADeviceTrigger5_typeE"></span><span id="_CPPv2N15HADeviceTrigger5_typeE"></span><span id="HADeviceTrigger::_type__cCP"></span><span class="target" id="class_h_a_device_trigger_1a88b1d1090e345184ec9deedce27af780"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_type</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger5_typeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Pointer to the trigger’s type. It can be pointer to the flash memory. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger8_subtypeE">
<span id="_CPPv3N15HADeviceTrigger8_subtypeE"></span><span id="_CPPv2N15HADeviceTrigger8_subtypeE"></span><span id="HADeviceTrigger::_subtype__cCP"></span><span class="target" id="class_h_a_device_trigger_1acf4b0a3c45c848d98023d9f52713b247"></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_subtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger8_subtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Pointer to the trigger’s subtype. It can be pointer to the flash memory. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger14_isProgmemTypeE">
<span id="_CPPv3N15HADeviceTrigger14_isProgmemTypeE"></span><span id="_CPPv2N15HADeviceTrigger14_isProgmemTypeE"></span><span id="HADeviceTrigger::_isProgmemType__b"></span><span class="target" id="class_h_a_device_trigger_1a7b35e05003f99d5335f1716c7850db13"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_isProgmemType</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger14_isProgmemTypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Specifies whether the type points to the flash memory. </p>
</dd></dl>

<dl class="cpp var">
<dt class="sig sig-object cpp" id="_CPPv4N15HADeviceTrigger17_isProgmemSubtypeE">
<span id="_CPPv3N15HADeviceTrigger17_isProgmemSubtypeE"></span><span id="_CPPv2N15HADeviceTrigger17_isProgmemSubtypeE"></span><span id="HADeviceTrigger::_isProgmemSubtype__b"></span><span class="target" id="class_h_a_device_trigger_1ae03e2e8c6b6133b71d00bf2d9168feaa"></span><span class="kt"><span class="pre">bool</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_isProgmemSubtype</span></span></span><a class="headerlink" href="#_CPPv4N15HADeviceTrigger17_isProgmemSubtypeE" title="Permalink to this definition">¶</a><br /></dt>
<dd><p>Specifies whether the subtype points to the flash memory. </p>
</dd></dl>

</div>
</dd></dl>

</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="ha-device-tracker.html"
       title="previous chapter">← HADeviceTracker class</a>
  </li>
  <li class="next">
    <a href="ha-fan.html"
       title="next chapter">HAFan class →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>