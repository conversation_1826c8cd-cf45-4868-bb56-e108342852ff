<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>Prerequisites - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="Installation" href="installation.html" />
  <link rel="prev" title="Getting started" href="index.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Getting started</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../library/index.html">Library</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../library/introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../library/compiler-macros.html">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="index.html">Getting started</a> &raquo;</li>
    
    <li>Prerequisites</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="index.html"
       title="previous chapter">← Getting started</a>
  </li>
  <li class="next">
    <a href="installation.html"
       title="next chapter">Installation →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="prerequisites">
<h1>Prerequisites<a class="headerlink" href="#prerequisites" title="Permalink to this headline">¶</a></h1>
<p>ArduinoHA uses MQTT protocol over TCP to communicate with your Home Assistant instance.
The MQTT broker is not installed by default so you will need to install an extra HA addon in order to get the integration working.</p>
<p>The recommended setup method is to use the <a class="reference external" href="https://github.com/home-assistant/addons/blob/master/mosquitto/DOCS.md">Mosquitto Brokker addon</a>.
After installing the addon your HA instance acts as a broker for all devices you’re going to integrate with it.</p>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="index.html"
       title="previous chapter">← Getting started</a>
  </li>
  <li class="next">
    <a href="installation.html"
       title="next chapter">Installation →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>