<!DOCTYPE html>
<html >
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1"><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

      <title>Compiler macros - ArduinoHA</title>
    
          <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
          <link rel="stylesheet" href="../../_static/theme.css " type="text/css" />
          <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
      
      <!-- sphinx script_files -->
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/custom.js"></script>

      
      <!-- bundled in js (rollup iife) -->
      <!-- <script src="../../_static/theme-vendors.js"></script> -->
      <script src="../../_static/theme.js" defer></script>
    
  <link rel="index" title="Index" href="../../genindex.html" />
  <link rel="search" title="Search" href="../../search.html" />
  <link rel="next" title="API reference" href="../api/index.html" />
  <link rel="prev" title="MQTT advanced features" href="mqtt-advanced.html" /> 
  </head>

  <body>
    <div id="app">
    <div class="theme-container" :class="pageClasses"><navbar @toggle-sidebar="toggleSidebar">
  <router-link to="../../index.html" class="home-link">
    
      <span class="site-name">ArduinoHA</span>
    
  </router-link>

  <div class="links">
    <navlinks class="can-hide">



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

    </navlinks>
  </div>
</navbar>

      
      <div class="sidebar-mask" @click="toggleSidebar(false)">
      </div>
        <sidebar @toggle-sidebar="toggleSidebar">
          
          <navlinks>
            



  
    <div class="nav-item">
      <a href="https://github.com/dawidchyrzynski/arduino-home-assistant"
        class="nav-link external">
          Github <outboundlink></outboundlink>
      </a>
    </div>
  

            
          </navlinks><div id="searchbox" class="searchbox" role="search">
  <div class="caption"><span class="caption-text">Quick search</span>
    <div class="searchformwrapper">
      <form class="search" action="../../search.html" method="get">
        <input type="text" name="q" />
        <input type="submit" value="Search" />
        <input type="hidden" name="check_keywords" value="yes" />
        <input type="hidden" name="area" value="default" />
      </form>
    </div>
  </div>
</div><div class="sidebar-links" role="navigation" aria-label="main navigation">
  <p class="caption" role="heading"><span class="caption-text">Docs</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting-started/index.html">Getting started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/prerequisites.html">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/installation.html">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/compatible-hardware.html">Compatible Hardware</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting-started/examples.html">Examples</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Library</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="introduction.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-configuration.html">Device configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="availability-reporting.html">Availability reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="connection-params.html">Connection parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="discovery.html">Discovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="device-types.html">Device types (entities)</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-security.html">MQTT security</a></li>
<li class="toctree-l2"><a class="reference internal" href="mqtt-advanced.html">MQTT advanced features</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Compiler macros</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/core/index.html">Core API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/device-types/index.html">Device types API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/utils/index.html">Utils API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference external" href="https://github.com/sponsors/dawidchyrzynski">Sponsor</a></li>
</ul>

</div>
        </sidebar>

      <page>
          <div class="body-header" role="navigation" aria-label="navigation">
  
  <ul class="breadcrumbs">
    <li><a href="../../index.html">Docs</a> &raquo;</li>
    
      <li><a href="index.html">Library</a> &raquo;</li>
    
    <li>Compiler macros</li>
  </ul>
  

  <ul class="page-nav">
  <li class="prev">
    <a href="mqtt-advanced.html"
       title="previous chapter">← MQTT advanced features</a>
  </li>
  <li class="next">
    <a href="../api/index.html"
       title="next chapter">API reference →</a>
  </li>
</ul>
  
</div>
<hr>
          <div class="content" role="main" v-pre>
            
  <section id="compiler-macros">
<h1>Compiler macros<a class="headerlink" href="#compiler-macros" title="Permalink to this headline">¶</a></h1>
<p>The library supports couple of macros that are defined in the <cite>ArduinoHADefines.h</cite> file.
You can uncomment them in this specific file or provide flags directly to compiler (for example: via Makefile).</p>
<section id="debug-mode">
<h2>Debug mode<a class="headerlink" href="#debug-mode" title="Permalink to this headline">¶</a></h2>
<p>Debug mode unlocks logging feature in the library.
Logs may be useful for debugging the communication with the Home Assistant.</p>
<p>To enable debug mode you need to defined <cite>ARDUINOHA_DEBUG</cite> macro.</p>
</section>
<section id="code-optimization">
<h2>Code optimization<a class="headerlink" href="#code-optimization" title="Permalink to this headline">¶</a></h2>
<p>Defining one of the macros listed below results in truncating the corresponding device type.
It may be useful if you want to save some flash memory occupied by virtual tables of those classes.</p>
<ul class="simple">
<li><p><cite>EX_ARDUINOHA_BINARY_SENSOR</cite></p></li>
<li><p><cite>EX_ARDUINOHA_BUTTON</cite></p></li>
<li><p><cite>EX_ARDUINOHA_CAMERA</cite></p></li>
<li><p><cite>EX_ARDUINOHA_COVER</cite></p></li>
<li><p><cite>EX_ARDUINOHA_DEVICE_TRACKER</cite></p></li>
<li><p><cite>EX_ARDUINOHA_DEVICE_TRIGGER</cite></p></li>
<li><p><cite>EX_ARDUINOHA_FAN</cite></p></li>
<li><p><cite>EX_ARDUINOHA_HVAC</cite></p></li>
<li><p><cite>EX_ARDUINOHA_LIGHT</cite></p></li>
<li><p><cite>EX_ARDUINOHA_LOCK</cite></p></li>
<li><p><cite>EX_ARDUINOHA_NUMBER</cite></p></li>
<li><p><cite>EX_ARDUINOHA_SCENE</cite></p></li>
<li><p><cite>EX_ARDUINOHA_SELECT</cite></p></li>
<li><p><cite>EX_ARDUINOHA_SENSOR</cite></p></li>
<li><p><cite>EX_ARDUINOHA_SWITCH</cite></p></li>
<li><p><cite>EX_ARDUINOHA_TAG_SCANNER</cite></p></li>
</ul>
</section>
</section>


          </div>
          <div class="page-nav">
            <div class="inner"><ul class="page-nav">
  <li class="prev">
    <a href="mqtt-advanced.html"
       title="previous chapter">← MQTT advanced features</a>
  </li>
  <li class="next">
    <a href="../api/index.html"
       title="next chapter">API reference →</a>
  </li>
</ul><div class="footer" role="contentinfo">
      &#169; Copyright 2022, Dawid Chyrzynski.
    <br>
    Created using <a href="http://sphinx-doc.org/">Sphinx</a> 4.5.0 with <a href="https://github.com/schettino72/sphinx_press_theme">Press Theme</a> 0.8.0.
</div>
            </div>
          </div>
      </page>
    </div></div>
    
    
  </body>
</html>