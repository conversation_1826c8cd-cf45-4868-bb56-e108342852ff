#include <Preferences.h>

class MotorController {
private:
  int motorId;
  int stepPin;
  int dirPin;
  int enablePin;
  int targetPosition;
  int currentPosition;
  int maxPosition;
  bool isEnabled;
  bool haveUpdate;
  unsigned long lastStepTime;
  Preferences preferences;

public:
  MotorController(int motorId, int stepPin, int dirPin, int enablePin);

  void begin();

  void enable();

  void disable();

  void goToPosition(int targetPosition);

  void open();

  void stop();

  void close();

  void update();

  int getCurrentPositionNormalized();

  void setCurrentPosition(int position);

  int getCurrentPosition() const;

  void setMaxPosition(int maxPos);

  int getMaxPosition();

  bool getIsEnabled();

  void resetPosition();

  bool getHaveUpdate();

  void clearHaveUpdate();

private:
  void saveCurrentPositionToPreferences();
};
