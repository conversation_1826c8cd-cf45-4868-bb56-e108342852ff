Examples
========

.. list-table::
   :widths: 25 75
   :header-rows: 1
   :class: examples-table

   * - Example
     - Description
   * - :example:`Binary sensor <binary-sensor/binary-sensor.ino>`
     - Using the binary sensor as a door contact sensor.
   * - :example:`Button <button/button.ino>`
     - Adding simple buttons to the Home Assistant panel.
   * - :example:`Camera <esp32-cam/esp32-cam.ino>`
     - Publishing the preview from the ESP32-CAM module.
   * - :example:`Cover <cover/cover.ino>`
     - Controlling a window cover (open / close / stop).
   * - :example:`Device trigger <multi-state-button/multi-state-button.ino>`
     - Implementation of a simple wall switch that reports press and hold states.
   * - :example:`Fan <fan/fan.ino>`
     - Controlling a simple fan (state + speed).
   * - :example:`HVAC <hvac/hvac.ino>`
     - HVAC controller with multiple modes, power control and target temperature.
   * - :example:`Lock <lock/lock.ino>`
     - A simple door lock that's controlled by the Home Assistant.
   * - :example:`Light <light/light.ino>`
     - A simple light that allows changing brightness, color temperature and RGB color.
   * - :example:`Number <number/number.ino>`
     - Adding an interactive numeric slider in the Home Assistant panel.
   * - :example:`Scene <scene/scene.ino>`
     - Adding a custom scene in the Home Assistant panel. 
   * - :example:`Select <select/select.ino>`
     - A dropdown selector that's displayed in the Home Assistant panel.
   * - :example:`Sensor <sensor/sensor.ino>`
     - A simple sensor that reports a state in a string representation (open / opening / close).
   * - :example:`Analog sensor <sensor-analog/sensor-analog.ino>`
     - Reporting the analog pin's voltage to the Home Assistant.
   * - :example:`Integer sensor <sensor-integer/sensor-integer.ino>`
     - Reporting the device's uptime to the Home Assistant.
   * - :example:`Switch <led-switch/led-switch.ino>`
     - The LED that's controlled by the Home Assistant.
   * - :example:`Multi-switch <multi-switch/multi-switch.ino>`
     - Multiple switches controlled by the Home Assistant.
   * - :example:`Tag scanner <tag-scanner/tag-scanner.ino>`
     - Scanning RFID tags using the MFRC522 module.
   * - :example:`Availability <availability/availability.ino>`
     - Reporting entities' availability (online / offline) to the Home Assistant.
   * - :example:`Advanced availability <advanced-availability/advanced-availability.ino>`
     - Advanced availability reporting with MQTT LWT (Last Will and Testament).
   * - :example:`MQTT advanced <mqtt-advanced/mqtt-advanced.ino>`
     - Subscribing to custom topics and publishing custom messages.
   * - :example:`MQTT with credentials <mqtt-with-credentials/mqtt-with-credentials.ino>`
     - Establishing connection with a MQTT broker using the credentials. 
   * - :example:`NodeMCU (ESP8266) <nodemcu/nodemcu.ino>`
     - Basic example for ESP8266 devices.
   * - :example:`Arduino Nano 33 IoT <nano33iot/nano33iot.ino>`
     - Basic example for Arduino Nano 33 IoT (SAMD family).
   * - :example:`mDNS discovery <mdns/mdns.ino>`
     - Make your ESP8266 discoverable via the mDNS.
